require('dotenv').config();
const express = require('express');
const cors = require('cors');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
const { Airwallex } = require('@airwallex/node-sdk');
const admin = require('firebase-admin');

const airwallex = new Airwallex({
    apiKey: process.env.AIRWALLEX_API_KEY,
    clientId: process.env.AIRWALLEX_CLIENT_ID,
    env: 'demo' // Use 'prod' for production
});

// Initialize Firebase Admin
admin.initializeApp({
    credential: admin.credential.applicationDefault(),
});

const app = express();
app.use(cors());
app.use(express.json());

const PORT = process.env.PORT || 3000;

// Middleware to verify Firebase ID token
const verifyFirebaseToken = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({ error: 'Unauthorized' });
        }

        const idToken = authHeader.split('Bearer ')[1];
        const decodedToken = await admin.auth().verifyIdToken(idToken);
        req.user = decodedToken;
        next();
    } catch (error) {
        console.error('Error verifying Firebase token:', error);
        res.status(401).json({ error: 'Unauthorized' });
    }
};

app.get('/', (req, res) => {
    res.send('Dash backend is running!');
});

// MARK: - Stripe Connect Endpoints

// Create Express connected account
app.post('/stripe/create-account', verifyFirebaseToken, async (req, res) => {
    try {
        const { userId, email, displayName, phoneNumber } = req.body;

        // Verify the user making the request matches the userId
        if (req.user.uid !== userId) {
            return res.status(403).json({ error: 'Forbidden' });
        }

        const account = await stripe.accounts.create({
            type: 'express',
            country: 'US', // Default to US, can be made configurable
            email: email,
            capabilities: {
                card_payments: { requested: true },
                transfers: { requested: true },
            },
            business_type: 'individual',
            business_profile: {
                product_description: 'Dash Finance App - Split payments and money transfers',
            },
            individual: {
                email: email,
                first_name: displayName ? displayName.split(' ')[0] : undefined,
                last_name: displayName ? displayName.split(' ').slice(1).join(' ') : undefined,
                phone: phoneNumber,
            },
        });

        // Update user document in Firestore
        await admin.firestore().collection('users').doc(userId).update({
            stripeAccountId: account.id,
            stripeAccountStatus: 'pending_onboarding',
            stripeAccountCreatedAt: admin.firestore.FieldValue.serverTimestamp(),
        });

        res.json({
            success: true,
            accountId: account.id,
        });
    } catch (error) {
        console.error('Error creating Stripe account:', error);
        res.status(500).json({ error: error.message });
    }
});

// Create account link for onboarding
app.post('/stripe/create-account-link', verifyFirebaseToken, async (req, res) => {
    try {
        const { accountId, userId, returnUrl, refreshUrl } = req.body;

        // Verify the user owns this account
        const userDoc = await admin.firestore().collection('users').doc(userId).get();
        if (!userDoc.exists || userDoc.data().stripeAccountId !== accountId) {
            return res.status(403).json({ error: 'Forbidden' });
        }

        const accountLink = await stripe.accountLinks.create({
            account: accountId,
            refresh_url: refreshUrl,
            return_url: returnUrl,
            type: 'account_onboarding',
        });

        res.json({
            success: true,
            url: accountLink.url,
        });
    } catch (error) {
        console.error('Error creating account link:', error);
        res.status(500).json({ error: error.message });
    }
});

// Check account status
app.get('/stripe/account-status/:accountId', verifyFirebaseToken, async (req, res) => {
    try {
        const { accountId } = req.params;

        // Verify the user owns this account
        const userDoc = await admin.firestore().collection('users').doc(req.user.uid).get();
        if (!userDoc.exists || userDoc.data().stripeAccountId !== accountId) {
            return res.status(403).json({ error: 'Forbidden' });
        }

        const account = await stripe.accounts.retrieve(accountId);

        let status = 'pending_onboarding';
        if (account.details_submitted && account.charges_enabled) {
            status = 'active';
        } else if (account.details_submitted) {
            status = 'pending_verification';
        }

        // Update user document with current status
        await admin.firestore().collection('users').doc(req.user.uid).update({
            stripeAccountStatus: status,
        });

        res.json({
            success: true,
            status: status,
            chargesEnabled: account.charges_enabled,
            detailsSubmitted: account.details_submitted,
        });
    } catch (error) {
        console.error('Error checking account status:', error);
        res.status(500).json({ error: error.message });
    }
});

// Create setup intent for payment method
app.post('/stripe/create-setup-intent', verifyFirebaseToken, async (req, res) => {
    try {
        const { userId } = req.body;

        if (req.user.uid !== userId) {
            return res.status(403).json({ error: 'Forbidden' });
        }

        const setupIntent = await stripe.setupIntents.create({
            customer: await getOrCreateCustomer(userId, req.user.email),
            payment_method_types: ['card'],
            usage: 'off_session',
        });

        res.json({
            success: true,
            clientSecret: setupIntent.client_secret,
        });
    } catch (error) {
        console.error('Error creating setup intent:', error);
        res.status(500).json({ error: error.message });
    }
});

// Helper function to get or create Stripe customer
async function getOrCreateCustomer(userId, email) {
    try {
        // Check if user already has a customer ID
        const userDoc = await admin.firestore().collection('users').doc(userId).get();
        const userData = userDoc.data();

        if (userData && userData.stripeCustomerId) {
            return userData.stripeCustomerId;
        }

        // Create new customer
        const customer = await stripe.customers.create({
            email: email,
            metadata: {
                userId: userId,
            },
        });

        // Save customer ID to user document
        await admin.firestore().collection('users').doc(userId).update({
            stripeCustomerId: customer.id,
        });

        return customer.id;
    } catch (error) {
        console.error('Error getting or creating customer:', error);
        throw error;
    }
}

// Webhook endpoint for Stripe events
app.post('/stripe/webhook', express.raw({ type: 'application/json' }), async (req, res) => {
    const sig = req.headers['stripe-signature'];
    let event;

    try {
        event = stripe.webhooks.constructEvent(req.body, sig, process.env.STRIPE_WEBHOOK_SECRET);
    } catch (err) {
        console.error('Webhook signature verification failed:', err.message);
        return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    try {
        switch (event.type) {
            case 'account.updated':
                await handleAccountUpdated(event.data.object);
                break;
            case 'setup_intent.succeeded':
                await handleSetupIntentSucceeded(event.data.object);
                break;
            case 'payment_intent.succeeded':
                await handlePaymentIntentSucceeded(event.data.object);
                break;
            default:
                console.log(`Unhandled event type: ${event.type}`);
        }

        res.json({ received: true });
    } catch (error) {
        console.error('Error handling webhook:', error);
        res.status(500).json({ error: 'Webhook handler failed' });
    }
});

// Webhook handlers
async function handleAccountUpdated(account) {
    try {
        // Find user with this account ID
        const usersSnapshot = await admin.firestore()
            .collection('users')
            .where('stripeAccountId', '==', account.id)
            .get();

        if (usersSnapshot.empty) {
            console.log('No user found for account:', account.id);
            return;
        }

        const userDoc = usersSnapshot.docs[0];
        let status = 'pending_onboarding';

        if (account.details_submitted && account.charges_enabled) {
            status = 'active';
        } else if (account.details_submitted) {
            status = 'pending_verification';
        }

        await userDoc.ref.update({
            stripeAccountStatus: status,
            canJoinSplits: status === 'active',
        });

        console.log(`Updated account status for user ${userDoc.id}: ${status}`);
    } catch (error) {
        console.error('Error handling account update:', error);
    }
}

async function handleSetupIntentSucceeded(setupIntent) {
    try {
        const customerId = setupIntent.customer;
        const paymentMethodId = setupIntent.payment_method;

        // Find user with this customer ID
        const usersSnapshot = await admin.firestore()
            .collection('users')
            .where('stripeCustomerId', '==', customerId)
            .get();

        if (usersSnapshot.empty) {
            console.log('No user found for customer:', customerId);
            return;
        }

        const userDoc = usersSnapshot.docs[0];
        await userDoc.ref.update({
            stripePaymentMethodId: paymentMethodId,
            paymentMethodSetupAt: admin.firestore.FieldValue.serverTimestamp(),
        });

        console.log(`Payment method setup completed for user ${userDoc.id}`);
    } catch (error) {
        console.error('Error handling setup intent succeeded:', error);
    }
}

async function handlePaymentIntentSucceeded(paymentIntent) {
    try {
        // Handle successful payments for splits
        console.log('Payment succeeded:', paymentIntent.id);
        // Additional logic for updating split status, user balances, etc.
    } catch (error) {
        console.error('Error handling payment intent succeeded:', error);
    }
}

// MARK: - Split Payment Endpoints

// Create payment for split using pooled funds approach
app.post('/stripe/create-split-payment', verifyFirebaseToken, async (req, res) => {
    try {
        const { splitId, payingUserId, amount, creatorAccountId } = req.body;

        // Verify the user making the request
        if (req.user.uid !== payingUserId) {
            return res.status(403).json({ error: 'Forbidden' });
        }

        // Get creator's Stripe account
        const creatorDoc = await admin.firestore().collection('users').doc(creatorAccountId).get();
        if (!creatorDoc.exists) {
            return res.status(404).json({ error: 'Creator not found' });
        }

        const creatorData = creatorDoc.data();
        const creatorStripeAccountId = creatorData.stripeAccountId;

        if (!creatorStripeAccountId) {
            return res.status(400).json({ error: 'Creator has not set up Stripe Connect' });
        }

        // Create payment intent with destination charge (pooled funds approach)
        const paymentIntent = await stripe.paymentIntents.create({
            amount: Math.round(amount * 100), // Convert to cents
            currency: 'usd',
            payment_method_types: ['card'],
            transfer_data: {
                destination: creatorStripeAccountId,
            },
            metadata: {
                splitId: splitId,
                payingUserId: payingUserId,
                type: 'split_payment',
            },
        });

        res.json({
            success: true,
            paymentIntentId: paymentIntent.id,
        });
    } catch (error) {
        console.error('Error creating split payment:', error);
        res.status(500).json({ error: error.message });
    }
});

// Settle split by transferring funds to participants
app.post('/stripe/settle-split', verifyFirebaseToken, async (req, res) => {
    try {
        const { splitId, creatorId, participants, totalAmount } = req.body;

        // Verify the user is the split creator
        if (req.user.uid !== creatorId) {
            return res.status(403).json({ error: 'Only split creator can settle' });
        }

        // Get creator's Stripe account
        const creatorDoc = await admin.firestore().collection('users').doc(creatorId).get();
        const creatorData = creatorDoc.data();
        const creatorStripeAccountId = creatorData.stripeAccountId;

        if (!creatorStripeAccountId) {
            return res.status(400).json({ error: 'Creator has not set up Stripe Connect' });
        }

        const transferIds = {};
        const amountPerParticipant = totalAmount / participants.length;

        // Create transfers to each participant
        for (const participant of participants) {
            if (participant.userId !== creatorId) {
                // Get participant's Stripe account
                const participantDoc = await admin.firestore().collection('users').doc(participant.userId).get();
                const participantData = participantDoc.data();
                const participantStripeAccountId = participantData.stripeAccountId;

                if (participantStripeAccountId) {
                    const transfer = await stripe.transfers.create({
                        amount: Math.round(amountPerParticipant * 100), // Convert to cents
                        currency: 'usd',
                        destination: participantStripeAccountId,
                        metadata: {
                            splitId: splitId,
                            participantId: participant.userId,
                            type: 'split_settlement',
                        },
                    });

                    transferIds[participant.userId] = transfer.id;
                }
            }
        }

        res.json({
            success: true,
            transferIds: transferIds,
        });
    } catch (error) {
        console.error('Error settling split:', error);
        res.status(500).json({ error: error.message });
    }
});

// Add money to user account
app.post('/stripe/add-money', verifyFirebaseToken, async (req, res) => {
    try {
        const { userId, amount, paymentMethodId } = req.body;

        // Verify the user making the request
        if (req.user.uid !== userId) {
            return res.status(403).json({ error: 'Forbidden' });
        }

        // Get user's customer ID
        const userDoc = await admin.firestore().collection('users').doc(userId).get();
        const userData = userDoc.data();
        const customerId = userData.stripeCustomerId;

        if (!customerId) {
            return res.status(400).json({ error: 'User has not set up payment methods' });
        }

        // Create payment intent for adding money
        const paymentIntent = await stripe.paymentIntents.create({
            amount: Math.round(amount * 100), // Convert to cents
            currency: 'usd',
            customer: customerId,
            payment_method: paymentMethodId,
            confirmation_method: 'manual',
            confirm: true,
            return_url: 'dash://payment-complete',
            metadata: {
                userId: userId,
                type: 'add_money',
            },
        });

        res.json({
            success: true,
            paymentIntentId: paymentIntent.id,
        });
    } catch (error) {
        console.error('Error adding money:', error);
        res.status(500).json({ error: error.message });
    }
});

// Stripe Payment Intent Endpoint
app.post('/create-payment-intent', async (req, res) => {
    const { amount, currency } = req.body;

    try {
        const paymentIntent = await stripe.paymentIntents.create({
            amount,
            currency,
            payment_method_types: ['card'],
        });

        res.status(200).send({
            clientSecret: paymentIntent.client_secret,
        });
    } catch (error) {
        res.status(500).send({ error: error.message });
    }
});

// Airwallex Create Cardholder Endpoint
app.post('/create-cardholder', async (req, res) => {
    try {
        const cardholder = await airwallex.issuing.createCardholder({
            // In a real app, you'd get this from the user's profile
            first_name: 'John',
            last_name: 'Doe',
            email: '<EMAIL>',
            phone_number: '13000000000',
            address: {
                street: '180 Lonsdale Street',
                city: 'Melbourne',
                state: 'VIC',
                country_code: 'AU',
                postcode: '3000',
            },
        });
        res.status(200).send(cardholder);
    } catch (error) {
        console.error('Airwallex Error:', error);
        res.status(500).send({ error: error.message });
    }
});

// Airwallex Issue Virtual Card Endpoint
app.post('/issue-virtual-card', async (req, res) => {
    const { cardholder_id } = req.body;

    if (!cardholder_id) {
        return res.status(400).send({ error: 'Cardholder ID is required.' });
    }

    try {
        const card = await airwallex.issuing.issueCard({
            cardholder_id: cardholder_id,
            card_type: 'virtual',
            currency: 'AUD',
            spending_limits: [
                {
                    amount: 1000,
                    interval: 'per_transaction',
                },
            ],
        });
        res.status(200).send(card);
    } catch (error) {
        console.error('Airwallex Error:', error);
        res.status(500).send({ error: error.message });
    }
});

// TODO: Add endpoint to issue virtual card to a cardholder

app.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}`);
}); 