const functions = require('firebase-functions');
const admin = require('firebase-admin');
const express = require('express');
const cors = require('cors');
const stripe = require('stripe')(functions.config().stripe.secret_key);

admin.initializeApp();

// Create Express app for HTTP functions
const app = express();
app.use(cors({ origin: true }));
app.use(express.json());
app.use(express.raw({ type: 'application/json' }));

// Middleware to verify Firebase ID token
const verifyFirebaseToken = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({ error: 'Unauthorized' });
        }

        const idToken = authHeader.split('Bearer ')[1];
        const decodedToken = await admin.auth().verifyIdToken(idToken);
        req.user = decodedToken;
        next();
    } catch (error) {
        console.error('Error verifying token:', error);
        return res.status(401).json({ error: 'Unauthorized' });
    }
};

// Send push notification when a transaction is created
exports.sendTransactionNotification = functions.firestore
  .document('users/{userId}/transactions/{transactionId}')
  .onCreate(async (snap, context) => {
    try {
      const transaction = snap.data();
      const userId = context.params.userId;
      const transactionId = context.params.transactionId;
      
      console.log(`Processing transaction notification for user: ${userId}, transaction: ${transactionId}`);
      console.log('Transaction data:', transaction);
      
      // Only send notifications for money received (transfer type where current user is recipient)
      if (transaction.type !== 'transfer' || !transaction.recipientId || !transaction.senderId) {
        console.log('Not a transfer transaction or missing recipient/sender info, skipping notification');
        return null;
      }
      
      // Check if this transaction is for the recipient (money received)
      if (userId !== transaction.recipientId) {
        console.log('Transaction is not for recipient, skipping notification');
        return null;
      }
      
      // Get recipient's FCM token
      const recipientDoc = await admin.firestore()
        .collection('users')
        .doc(transaction.recipientId)
        .get();
      
      if (!recipientDoc.exists) {
        console.log('Recipient user document not found');
        return null;
      }
      
      const recipientData = recipientDoc.data();
      const fcmToken = recipientData.fcmToken;
      
      if (!fcmToken) {
        console.log('No FCM token found for recipient');
        return null;
      }
      
      // Format amount for display
      const amount = transaction.amount || 0;
      const formattedAmount = new Intl.NumberFormat('en-AU', {
        style: 'currency',
        currency: 'AUD'
      }).format(amount);
      
      const senderName = transaction.senderName || 'Someone';
      
      // Create notification payload
      const payload = {
        notification: {
          title: 'Money Received',
          body: `You received ${formattedAmount} from ${senderName}`,
          sound: 'default',
          badge: '1'
        },
        data: {
          type: 'money_received',
          amount: amount.toString(),
          senderName: senderName,
          senderId: transaction.senderId || '',
          transactionId: transactionId,
          click_action: 'FLUTTER_NOTIFICATION_CLICK'
        },
        apns: {
          payload: {
            aps: {
              sound: 'default',
              badge: 1,
              'content-available': 1
            }
          }
        },
        android: {
          priority: 'high',
          notification: {
            sound: 'default',
            priority: 'high',
            channelId: 'transaction_notifications'
          }
        }
      };
      
      console.log('Sending notification with payload:', payload);
      
      // Send the notification
      const response = await admin.messaging().sendToDevice(fcmToken, payload);
      
      console.log('Notification sent successfully:', response);
      
      // Log any failures
      if (response.failureCount > 0) {
        console.log('Some notifications failed:', response.results);
        
        // Check if token is invalid and remove it
        response.results.forEach((result, index) => {
          if (result.error) {
            console.log('Notification error:', result.error);
            
            // If token is invalid, remove it from user document
            if (result.error.code === 'messaging/invalid-registration-token' ||
                result.error.code === 'messaging/registration-token-not-registered') {
              console.log('Removing invalid FCM token');
              admin.firestore()
                .collection('users')
                .doc(transaction.recipientId)
                .update({
                  fcmToken: admin.firestore.FieldValue.delete()
                });
            }
          }
        });
      }
      
      return response;
      
    } catch (error) {
      console.error('Error sending transaction notification:', error);
      return null;
    }
  });

// Send push notification for money requests
exports.sendMoneyRequestNotification = functions.firestore
  .document('money_requests/{requestId}')
  .onCreate(async (snap, context) => {
    try {
      const request = snap.data();
      const requestId = context.params.requestId;
      
      console.log(`Processing money request notification: ${requestId}`);
      
      // Get requestee's FCM token
      const requesteeDoc = await admin.firestore()
        .collection('users')
        .doc(request.requesteeId)
        .get();
      
      if (!requesteeDoc.exists) {
        console.log('Requestee user document not found');
        return null;
      }
      
      const requesteeData = requesteeDoc.data();
      const fcmToken = requesteeData.fcmToken;
      
      if (!fcmToken) {
        console.log('No FCM token found for requestee');
        return null;
      }
      
      // Get requester's name
      const requesterDoc = await admin.firestore()
        .collection('users')
        .doc(request.requesterId)
        .get();
      
      const requesterName = requesterDoc.exists ? 
        (requesterDoc.data().displayName || 'Someone') : 'Someone';
      
      // Format amount
      const formattedAmount = new Intl.NumberFormat('en-AU', {
        style: 'currency',
        currency: 'AUD'
      }).format(request.amount);
      
      // Create notification payload
      const payload = {
        notification: {
          title: 'Money Request',
          body: `${requesterName} requested ${formattedAmount} from you`,
          sound: 'default',
          badge: '1'
        },
        data: {
          type: 'money_request',
          amount: request.amount.toString(),
          requesterName: requesterName,
          requesterId: request.requesterId,
          requestId: requestId,
          click_action: 'FLUTTER_NOTIFICATION_CLICK'
        },
        apns: {
          payload: {
            aps: {
              sound: 'default',
              badge: 1,
              'content-available': 1
            }
          }
        }
      };
      
      // Send the notification
      const response = await admin.messaging().sendToDevice(fcmToken, payload);
      console.log('Money request notification sent:', response);
      
      return response;
      
    } catch (error) {
      console.error('Error sending money request notification:', error);
      return null;
    }
  });

// Send push notification for split invitations
exports.sendSplitNotification = functions.firestore
  .document('splits/{splitId}')
  .onCreate(async (snap, context) => {
    try {
      const split = snap.data();
      const splitId = context.params.splitId;
      
      console.log(`Processing split notification: ${splitId}`);
      
      // Get creator's name
      const creatorDoc = await admin.firestore()
        .collection('users')
        .doc(split.creatorId)
        .get();
      
      const creatorName = creatorDoc.exists ? 
        (creatorDoc.data().displayName || 'Someone') : 'Someone';
      
      // Format amount
      const formattedAmount = new Intl.NumberFormat('en-AU', {
        style: 'currency',
        currency: 'AUD'
      }).format(split.totalAmount);
      
      // Send notifications to all participants except creator
      const participants = split.participants || [];
      const notificationPromises = [];
      
      for (const participant of participants) {
        if (participant.userId !== split.creatorId) {
          // Get participant's FCM token
          const participantDoc = await admin.firestore()
            .collection('users')
            .doc(participant.userId)
            .get();
          
          if (participantDoc.exists) {
            const participantData = participantDoc.data();
            const fcmToken = participantData.fcmToken;
            
            if (fcmToken) {
              const payload = {
                notification: {
                  title: 'New Split Created',
                  body: `${creatorName} created a split "${split.title}" for ${formattedAmount}`,
                  sound: 'default',
                  badge: '1'
                },
                data: {
                  type: 'split_created',
                  splitTitle: split.title || '',
                  amount: split.totalAmount.toString(),
                  creatorName: creatorName,
                  splitId: splitId,
                  click_action: 'FLUTTER_NOTIFICATION_CLICK'
                },
                apns: {
                  payload: {
                    aps: {
                      sound: 'default',
                      badge: 1,
                      'content-available': 1
                    }
                  }
                }
              };
              
              notificationPromises.push(
                admin.messaging().sendToDevice(fcmToken, payload)
              );
            }
          }
        }
      }
      
      const responses = await Promise.all(notificationPromises);
      console.log('Split notifications sent:', responses.length);
      
      return responses;
      
    } catch (error) {
      console.error('Error sending split notification:', error);
      return null;
    }
  });

// Clean up invalid FCM tokens
exports.cleanupInvalidTokens = functions.pubsub
  .schedule('every 24 hours')
  .onRun(async (context) => {
    console.log('Starting FCM token cleanup');
    
    try {
      // Get all users with FCM tokens older than 30 days
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - 30);
      
      const usersSnapshot = await admin.firestore()
        .collection('users')
        .where('lastTokenUpdate', '<', admin.firestore.Timestamp.fromDate(cutoffDate))
        .get();
      
      const batch = admin.firestore().batch();
      let cleanupCount = 0;
      
      usersSnapshot.forEach(doc => {
        const userData = doc.data();
        if (userData.fcmToken) {
          batch.update(doc.ref, {
            fcmToken: admin.firestore.FieldValue.delete(),
            lastTokenUpdate: admin.firestore.FieldValue.delete()
          });
          cleanupCount++;
        }
      });
      
      if (cleanupCount > 0) {
        await batch.commit();
        console.log(`Cleaned up ${cleanupCount} old FCM tokens`);
      } else {
        console.log('No old FCM tokens to clean up');
      }
      
      return null;
      
    } catch (error) {
      console.error('Error during FCM token cleanup:', error);
      return null;
    }
  });

// Admin function to get all users for notifications
exports.getAllUsers = functions.https.onCall(async (data, context) => {
  try {
    console.log('getAllUsers called');

    // For security, you might want to add admin authentication here
    // For now, allowing any authenticated user
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'Must be authenticated');
    }

    const usersSnapshot = await admin.firestore()
      .collection('users')
      .get();

    const users = [];
    usersSnapshot.forEach(doc => {
      const userData = doc.data();
      users.push({
        uid: doc.id,
        displayName: userData.displayName || 'Unknown User',
        email: userData.email || 'No email'
      });
    });

    console.log(`Returning ${users.length} users`);
    return { users };

  } catch (error) {
    console.error('Error in getAllUsers:', error);
    throw new functions.https.HttpsError('internal', 'Failed to get users');
  }
});

// Admin function to send broadcast notifications
exports.sendBroadcastNotification = functions.https.onCall(async (data, context) => {
  try {
    console.log('sendBroadcastNotification called with data:', data);

    // For security, you might want to add admin authentication here
    if (!context.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'Must be authenticated');
    }

    const { title, message, userIds, expiresAt } = data;

    if (!title || !message || !userIds || !Array.isArray(userIds)) {
      throw new functions.https.HttpsError('invalid-argument', 'Missing required fields');
    }

    const batch = admin.firestore().batch();
    let notificationCount = 0;

    // Create notification documents for each user
    for (const userId of userIds) {
      const notificationRef = admin.firestore().collection('notifications').doc();
      const notificationData = {
        userId: userId,
        title: title,
        message: message,
        type: 'admin_broadcast',
        priority: 'normal',
        isRead: false,
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      };

      // Add expiration if provided
      if (expiresAt) {
        notificationData.expiresAt = admin.firestore.Timestamp.fromDate(new Date(expiresAt));
      }

      batch.set(notificationRef, notificationData);
      notificationCount++;
    }

    await batch.commit();

    console.log(`Successfully created ${notificationCount} notifications`);
    return {
      success: true,
      message: `Notification sent to ${notificationCount} users`
    };

  } catch (error) {
    console.error('Error in sendBroadcastNotification:', error);
    throw new functions.https.HttpsError('internal', 'Failed to send notification');
  }
});

// MARK: - Stripe Connect Endpoints

// Create Stripe Connect Express account
app.post('/stripe/create-account', verifyFirebaseToken, async (req, res) => {
    try {
        const { userId } = req.body;

        // Verify the user making the request
        if (req.user.uid !== userId) {
            return res.status(403).json({ error: 'Forbidden' });
        }

        // Check if user already has a Stripe account
        const userDoc = await admin.firestore().collection('users').doc(userId).get();
        const userData = userDoc.data();

        if (userData && userData.stripeAccountId) {
            return res.json({
                success: true,
                accountId: userData.stripeAccountId,
                message: 'Account already exists'
            });
        }

        // Create Express account
        const account = await stripe.accounts.create({
            type: 'express',
            country: 'AU', // Australia
            email: userData.email,
            capabilities: {
                card_payments: { requested: true },
                transfers: { requested: true },
            },
            business_type: 'individual',
            metadata: {
                userId: userId,
                platform: 'dash'
            }
        });

        // Update user document with Stripe account ID
        await admin.firestore().collection('users').doc(userId).update({
            stripeAccountId: account.id,
            stripeAccountStatus: 'pending',
            stripeAccountCreatedAt: admin.firestore.FieldValue.serverTimestamp()
        });

        res.json({
            success: true,
            accountId: account.id
        });
    } catch (error) {
        console.error('Error creating Stripe account:', error);
        res.status(500).json({ error: error.message });
    }
});

// Create account onboarding link
app.post('/stripe/create-account-link', verifyFirebaseToken, async (req, res) => {
    try {
        const { accountId, userId } = req.body;

        // Verify the user making the request
        if (req.user.uid !== userId) {
            return res.status(403).json({ error: 'Forbidden' });
        }

        const accountLink = await stripe.accountLinks.create({
            account: accountId,
            refresh_url: 'dash://stripe-refresh',
            return_url: 'dash://stripe-return',
            type: 'account_onboarding',
        });

        res.json({
            success: true,
            url: accountLink.url
        });
    } catch (error) {
        console.error('Error creating account link:', error);
        res.status(500).json({ error: error.message });
    }
});

// Check account status
app.get('/stripe/account-status/:accountId', verifyFirebaseToken, async (req, res) => {
    try {
        const { accountId } = req.params;

        const account = await stripe.accounts.retrieve(accountId);

        const status = {
            detailsSubmitted: account.details_submitted,
            chargesEnabled: account.charges_enabled,
            payoutsEnabled: account.payouts_enabled,
            requirementsCurrentlyDue: account.requirements.currently_due,
            requirementsEventuallyDue: account.requirements.eventually_due
        };

        res.json({
            success: true,
            status: status
        });
    } catch (error) {
        console.error('Error checking account status:', error);
        res.status(500).json({ error: error.message });
    }
});

// Create payment for split using pooled funds approach
app.post('/stripe/create-split-payment', verifyFirebaseToken, async (req, res) => {
    try {
        const { splitId, payingUserId, amount, creatorAccountId } = req.body;

        // Verify the user making the request
        if (req.user.uid !== payingUserId) {
            return res.status(403).json({ error: 'Forbidden' });
        }

        // Get creator's Stripe account
        const creatorDoc = await admin.firestore().collection('users').doc(creatorAccountId).get();
        if (!creatorDoc.exists) {
            return res.status(404).json({ error: 'Creator not found' });
        }

        const creatorData = creatorDoc.data();
        const creatorStripeAccountId = creatorData.stripeAccountId;

        if (!creatorStripeAccountId) {
            return res.status(400).json({ error: 'Creator has not set up Stripe Connect' });
        }

        // Create payment intent with destination charge (pooled funds approach)
        const paymentIntent = await stripe.paymentIntents.create({
            amount: Math.round(amount * 100), // Convert to cents
            currency: 'aud',
            payment_method_types: ['card'],
            transfer_data: {
                destination: creatorStripeAccountId,
            },
            metadata: {
                splitId: splitId,
                payingUserId: payingUserId,
                type: 'split_payment',
            },
        });

        res.json({
            success: true,
            paymentIntentId: paymentIntent.id,
        });
    } catch (error) {
        console.error('Error creating split payment:', error);
        res.status(500).json({ error: error.message });
    }
});

// Settle split by transferring funds to participants
app.post('/stripe/settle-split', verifyFirebaseToken, async (req, res) => {
    try {
        const { splitId, creatorId, participants, totalAmount } = req.body;

        // Verify the user is the split creator
        if (req.user.uid !== creatorId) {
            return res.status(403).json({ error: 'Only split creator can settle' });
        }

        // Get creator's Stripe account
        const creatorDoc = await admin.firestore().collection('users').doc(creatorId).get();
        const creatorData = creatorDoc.data();
        const creatorStripeAccountId = creatorData.stripeAccountId;

        if (!creatorStripeAccountId) {
            return res.status(400).json({ error: 'Creator has not set up Stripe Connect' });
        }

        const transferIds = {};
        const amountPerParticipant = totalAmount / participants.length;

        // Create transfers to each participant
        for (const participant of participants) {
            if (participant.userId !== creatorId) {
                // Get participant's Stripe account
                const participantDoc = await admin.firestore().collection('users').doc(participant.userId).get();
                const participantData = participantDoc.data();
                const participantStripeAccountId = participantData.stripeAccountId;

                if (participantStripeAccountId) {
                    const transfer = await stripe.transfers.create({
                        amount: Math.round(amountPerParticipant * 100), // Convert to cents
                        currency: 'aud',
                        destination: participantStripeAccountId,
                        metadata: {
                            splitId: splitId,
                            participantId: participant.userId,
                            type: 'split_settlement',
                        },
                    });

                    transferIds[participant.userId] = transfer.id;
                }
            }
        }

        res.json({
            success: true,
            transferIds: transferIds,
        });
    } catch (error) {
        console.error('Error settling split:', error);
        res.status(500).json({ error: error.message });
    }
});

// Add money to user account
app.post('/stripe/add-money', verifyFirebaseToken, async (req, res) => {
    try {
        const { userId, amount, paymentMethodId } = req.body;

        // Verify the user making the request
        if (req.user.uid !== userId) {
            return res.status(403).json({ error: 'Forbidden' });
        }

        // Get user's customer ID
        const userDoc = await admin.firestore().collection('users').doc(userId).get();
        const userData = userDoc.data();
        const customerId = userData.stripeCustomerId;

        if (!customerId) {
            return res.status(400).json({ error: 'User has not set up payment methods' });
        }

        // Create payment intent for adding money
        const paymentIntent = await stripe.paymentIntents.create({
            amount: Math.round(amount * 100), // Convert to cents
            currency: 'aud',
            customer: customerId,
            payment_method: paymentMethodId,
            confirmation_method: 'manual',
            confirm: true,
            return_url: 'dash://payment-complete',
            metadata: {
                userId: userId,
                type: 'add_money',
            },
        });

        res.json({
            success: true,
            paymentIntentId: paymentIntent.id,
        });
    } catch (error) {
        console.error('Error adding money:', error);
        res.status(500).json({ error: error.message });
    }
});

// Stripe webhook handler
app.post('/stripe/webhook', async (req, res) => {
    const sig = req.headers['stripe-signature'];
    const webhookSecret = functions.config().stripe.webhook_secret;

    let event;

    try {
        event = stripe.webhooks.constructEvent(req.body, sig, webhookSecret);
    } catch (err) {
        console.error('Webhook signature verification failed:', err.message);
        return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    try {
        switch (event.type) {
            case 'account.updated':
                await handleAccountUpdated(event.data.object);
                break;
            case 'payment_intent.succeeded':
                await handlePaymentIntentSucceeded(event.data.object);
                break;
            case 'transfer.created':
                await handleTransferCreated(event.data.object);
                break;
            default:
                console.log(`Unhandled event type: ${event.type}`);
        }

        res.json({ received: true });
    } catch (error) {
        console.error('Error handling webhook:', error);
        res.status(500).json({ error: 'Webhook handler failed' });
    }
});

// Webhook event handlers
async function handleAccountUpdated(account) {
    try {
        const userId = account.metadata.userId;
        if (!userId) return;

        let status = 'pending';
        if (account.details_submitted && account.charges_enabled) {
            status = 'active';
        } else if (account.requirements.currently_due.length > 0) {
            status = 'requires_information';
        }

        await admin.firestore().collection('users').doc(userId).update({
            stripeAccountStatus: status,
            stripeAccountUpdatedAt: admin.firestore.FieldValue.serverTimestamp()
        });

        console.log(`Updated account status for user ${userId}: ${status}`);
    } catch (error) {
        console.error('Error handling account updated:', error);
    }
}

async function handlePaymentIntentSucceeded(paymentIntent) {
    try {
        console.log('Payment succeeded:', paymentIntent.id);
        // Additional logic for updating split status, user balances, etc.
    } catch (error) {
        console.error('Error handling payment intent succeeded:', error);
    }
}

async function handleTransferCreated(transfer) {
    try {
        console.log('Transfer created:', transfer.id);
        // Additional logic for tracking transfers
    } catch (error) {
        console.error('Error handling transfer created:', error);
    }
}

// Export the Express app as a Firebase Function
exports.api = functions.https.onRequest(app);
