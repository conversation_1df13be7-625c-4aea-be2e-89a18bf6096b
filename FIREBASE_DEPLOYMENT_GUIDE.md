# Firebase Functions Deployment Guide for Dash Stripe Connect

This guide explains how to deploy your Stripe Connect backend to Firebase Functions.

## Prerequisites

1. **Firebase CLI installed**:
   ```bash
   npm install -g firebase-tools
   ```

2. **Firebase project created** (if not already done):
   - Go to [Firebase Console](https://console.firebase.google.com)
   - Create a new project or use existing one
   - Enable Firestore and Authentication

## Setup Steps

### 1. Login to Firebase
```bash
firebase login
```

### 2. Initialize Firebase (if not already done)
```bash
cd backend
firebase init functions
```
- Select your Firebase project
- Choose JavaScript
- Install dependencies: Yes

### 3. Install Dependencies
```bash
cd functions
npm install
```

### 4. Configure Stripe Keys
Set your Stripe configuration using Firebase CLI:

```bash
# Set Stripe secret key
firebase functions:config:set stripe.secret_key="sk_test_51RqXNVEGqgdjTeexVTQfGwkq2bT3S6RwBaPRUB5Ldow2iYsUtT6gmzWwpwXZnXHPbTZVzfrqlSLQ9rk08v4MTC7200Epk5Vdcs"

# Set webhook secret (get this from Stripe Dashboard)
firebase functions:config:set stripe.webhook_secret="whsec_your_webhook_secret_here"
```

### 5. Deploy Functions
```bash
firebase deploy --only functions
```

### 6. Get Your Function URL
After deployment, you'll get a URL like:
```
https://us-central1-your-project-id.cloudfunctions.net/api
```

### 7. Update iOS App Configuration
Update the baseURL in your iOS services:

**StripeConnectService.swift**:
```swift
private let baseURL = "https://us-central1-YOUR-PROJECT-ID.cloudfunctions.net/api"
```

**StripeConnectPaymentService.swift**:
```swift
private let baseURL = "https://us-central1-YOUR-PROJECT-ID.cloudfunctions.net/api"
```

Replace `YOUR-PROJECT-ID` with your actual Firebase project ID.

## Stripe Webhook Configuration

### 1. Configure Webhooks in Stripe Dashboard
1. Go to [Stripe Dashboard](https://dashboard.stripe.com/webhooks)
2. Click "Add endpoint"
3. Set endpoint URL: `https://us-central1-YOUR-PROJECT-ID.cloudfunctions.net/api/stripe/webhook`
4. Select events to listen for:
   - `account.updated`
   - `payment_intent.succeeded`
   - `transfer.created`

### 2. Update Webhook Secret
After creating the webhook, copy the signing secret and update your Firebase config:
```bash
firebase functions:config:set stripe.webhook_secret="whsec_your_actual_webhook_secret"
firebase deploy --only functions
```

## Available Endpoints

Your deployed Firebase Functions will provide these endpoints:

### Stripe Connect Management
- `POST /stripe/create-account` - Create Express account
- `POST /stripe/create-account-link` - Generate onboarding link
- `GET /stripe/account-status/:accountId` - Check account status

### Payment Processing
- `POST /stripe/create-split-payment` - Process split payment
- `POST /stripe/settle-split` - Settle split with transfers
- `POST /stripe/add-money` - Add money to user account

### Webhooks
- `POST /stripe/webhook` - Handle Stripe webhook events

### Existing Functions (unchanged)
- `sendTransactionNotification` - Firestore trigger
- `sendMoneyRequestNotification` - Firestore trigger
- `sendSplitNotification` - Firestore trigger
- `cleanupInvalidTokens` - Scheduled function
- `getAllUsers` - Callable function
- `sendBroadcastNotification` - Callable function

## Testing

### 1. Test Local Development
```bash
cd functions
firebase emulators:start --only functions
```
This runs functions locally at `http://localhost:5001/YOUR-PROJECT-ID/us-central1/api`

### 2. Test Production Deployment
Use your deployed function URL in the iOS app and test:
- User onboarding flow
- Split creation and payment
- Settlement process

## Security Considerations

### 1. Firebase Security Rules
Ensure your Firestore security rules are properly configured:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Splits are readable by participants
    match /splits/{splitId} {
      allow read: if request.auth != null && 
        request.auth.uid in resource.data.participants[].userId;
      allow write: if request.auth != null && 
        request.auth.uid == resource.data.creatorId;
    }
  }
}
```

### 2. Function Security
- All endpoints require Firebase Authentication
- User verification prevents unauthorized access
- Stripe webhook signatures are verified

## Monitoring and Logs

### 1. View Function Logs
```bash
firebase functions:log
```

### 2. Firebase Console
Monitor function performance and errors in the Firebase Console:
- Go to Functions section
- View logs, metrics, and error reports

## Troubleshooting

### Common Issues

1. **Configuration not found**:
   ```bash
   firebase functions:config:get
   ```
   Verify your Stripe keys are set correctly.

2. **CORS errors**:
   The Express app includes CORS middleware for all origins.

3. **Authentication errors**:
   Ensure Firebase ID tokens are being sent in Authorization headers.

4. **Webhook verification fails**:
   Double-check your webhook secret configuration.

### Debug Commands
```bash
# View current configuration
firebase functions:config:get

# View function logs
firebase functions:log --only api

# Test functions locally
firebase emulators:start --only functions
```

## Production Checklist

- [ ] Stripe keys configured in Firebase Functions
- [ ] Webhook endpoint configured in Stripe Dashboard
- [ ] iOS app updated with production function URL
- [ ] Firestore security rules configured
- [ ] Functions deployed successfully
- [ ] Webhook signature verification working
- [ ] End-to-end testing completed

## Cost Considerations

Firebase Functions pricing:
- 2 million invocations per month (free tier)
- $0.40 per million invocations after free tier
- Compute time: $0.0000025 per 100ms

For a typical Dash app usage, costs should be minimal during development and testing.

## Next Steps

1. Deploy your functions using the steps above
2. Test the complete Stripe Connect flow
3. Configure production webhooks
4. Monitor function performance
5. Set up proper error handling and alerting

Your Stripe Connect integration is now ready for production deployment on Firebase Functions!
