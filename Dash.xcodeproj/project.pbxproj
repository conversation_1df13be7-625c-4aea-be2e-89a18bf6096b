// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		2B55795DD4B9CC1B1CB6FED3 /* Pods_Dash.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B4302F46076CF9A8E7F6CCE5 /* Pods_Dash.framework */; };
		B80A9B722E3AD5660048AA71 /* Stripe in Frameworks */ = {isa = PBXBuildFile; productRef = B80A9B712E3AD5660048AA71 /* Stripe */; };
		B80A9B742E3AD5660048AA71 /* StripeApplePay in Frameworks */ = {isa = PBXBuildFile; productRef = B80A9B732E3AD5660048AA71 /* StripeApplePay */; };
		B80A9B762E3AD5660048AA71 /* StripeCardScan in Frameworks */ = {isa = PBXBuildFile; productRef = B80A9B752E3AD5660048AA71 /* StripeCardScan */; };
		B80A9B782E3AD5660048AA71 /* StripeConnect in Frameworks */ = {isa = PBXBuildFile; productRef = B80A9B772E3AD5660048AA71 /* StripeConnect */; };
		B80A9B7A2E3AD5660048AA71 /* StripeFinancialConnections in Frameworks */ = {isa = PBXBuildFile; productRef = B80A9B792E3AD5660048AA71 /* StripeFinancialConnections */; };
		B89BD86C2DFC5330002E7017 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = B89BD86B2DFC5330002E7017 /* GoogleService-Info.plist */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		B89BD83A2DFC4F2A002E7017 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B89BD8242DFC4F29002E7017 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B89BD82B2DFC4F29002E7017;
			remoteInfo = Dash;
		};
		B89BD8442DFC4F2A002E7017 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B89BD8242DFC4F29002E7017 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B89BD82B2DFC4F29002E7017;
			remoteInfo = Dash;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		B4302F46076CF9A8E7F6CCE5 /* Pods_Dash.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Dash.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		B7D3F39C16D0D07DB58269FB /* Pods-Dash.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Dash.release.xcconfig"; path = "Target Support Files/Pods-Dash/Pods-Dash.release.xcconfig"; sourceTree = "<group>"; };
		B89BD82C2DFC4F29002E7017 /* Dash.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Dash.app; sourceTree = BUILT_PRODUCTS_DIR; };
		B89BD8392DFC4F2A002E7017 /* DashTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = DashTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		B89BD8432DFC4F2A002E7017 /* DashUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = DashUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		B89BD86B2DFC5330002E7017 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		DCD4286E7D3A630910667D5D /* Pods-Dash.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Dash.debug.xcconfig"; path = "Target Support Files/Pods-Dash/Pods-Dash.debug.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		B89BD8862DFC5661002E7017 /* Exceptions for "Dash" folder in "Dash" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = B89BD82B2DFC4F29002E7017 /* Dash */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		B89BD82E2DFC4F29002E7017 /* Dash */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				B89BD8862DFC5661002E7017 /* Exceptions for "Dash" folder in "Dash" target */,
			);
			path = Dash;
			sourceTree = "<group>";
		};
		B89BD83C2DFC4F2A002E7017 /* DashTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = DashTests;
			sourceTree = "<group>";
		};
		B89BD8462DFC4F2A002E7017 /* DashUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = DashUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		B89BD8292DFC4F29002E7017 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B80A9B762E3AD5660048AA71 /* StripeCardScan in Frameworks */,
				B80A9B7A2E3AD5660048AA71 /* StripeFinancialConnections in Frameworks */,
				B80A9B742E3AD5660048AA71 /* StripeApplePay in Frameworks */,
				2B55795DD4B9CC1B1CB6FED3 /* Pods_Dash.framework in Frameworks */,
				B80A9B722E3AD5660048AA71 /* Stripe in Frameworks */,
				B80A9B782E3AD5660048AA71 /* StripeConnect in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B89BD8362DFC4F2A002E7017 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B89BD8402DFC4F2A002E7017 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		4E097AC5799F25F068E36A66 /* Pods */ = {
			isa = PBXGroup;
			children = (
				DCD4286E7D3A630910667D5D /* Pods-Dash.debug.xcconfig */,
				B7D3F39C16D0D07DB58269FB /* Pods-Dash.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		8992D1B3BD6B305095E9CFA4 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				B4302F46076CF9A8E7F6CCE5 /* Pods_Dash.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		B89BD8232DFC4F29002E7017 = {
			isa = PBXGroup;
			children = (
				B89BD82E2DFC4F29002E7017 /* Dash */,
				B89BD83C2DFC4F2A002E7017 /* DashTests */,
				B89BD8462DFC4F2A002E7017 /* DashUITests */,
				B89BD82D2DFC4F29002E7017 /* Products */,
				B89BD86B2DFC5330002E7017 /* GoogleService-Info.plist */,
				4E097AC5799F25F068E36A66 /* Pods */,
				8992D1B3BD6B305095E9CFA4 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		B89BD82D2DFC4F29002E7017 /* Products */ = {
			isa = PBXGroup;
			children = (
				B89BD82C2DFC4F29002E7017 /* Dash.app */,
				B89BD8392DFC4F2A002E7017 /* DashTests.xctest */,
				B89BD8432DFC4F2A002E7017 /* DashUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		B89BD82B2DFC4F29002E7017 /* Dash */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B89BD84D2DFC4F2A002E7017 /* Build configuration list for PBXNativeTarget "Dash" */;
			buildPhases = (
				5E681D3D17CFD29D6C2A81B4 /* [CP] Check Pods Manifest.lock */,
				B89BD8282DFC4F29002E7017 /* Sources */,
				B89BD8292DFC4F29002E7017 /* Frameworks */,
				B89BD82A2DFC4F29002E7017 /* Resources */,
				5B7DB9648C0AD75472123396 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				B89BD82E2DFC4F29002E7017 /* Dash */,
			);
			name = Dash;
			productName = Dash;
			productReference = B89BD82C2DFC4F29002E7017 /* Dash.app */;
			productType = "com.apple.product-type.application";
		};
		B89BD8382DFC4F2A002E7017 /* DashTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B89BD8502DFC4F2A002E7017 /* Build configuration list for PBXNativeTarget "DashTests" */;
			buildPhases = (
				B89BD8352DFC4F2A002E7017 /* Sources */,
				B89BD8362DFC4F2A002E7017 /* Frameworks */,
				B89BD8372DFC4F2A002E7017 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				B89BD83B2DFC4F2A002E7017 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				B89BD83C2DFC4F2A002E7017 /* DashTests */,
			);
			name = DashTests;
			productName = DashTests;
			productReference = B89BD8392DFC4F2A002E7017 /* DashTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		B89BD8422DFC4F2A002E7017 /* DashUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B89BD8532DFC4F2A002E7017 /* Build configuration list for PBXNativeTarget "DashUITests" */;
			buildPhases = (
				B89BD83F2DFC4F2A002E7017 /* Sources */,
				B89BD8402DFC4F2A002E7017 /* Frameworks */,
				B89BD8412DFC4F2A002E7017 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				B89BD8452DFC4F2A002E7017 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				B89BD8462DFC4F2A002E7017 /* DashUITests */,
			);
			name = DashUITests;
			productName = DashUITests;
			productReference = B89BD8432DFC4F2A002E7017 /* DashUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		B89BD8242DFC4F29002E7017 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					B89BD82B2DFC4F29002E7017 = {
						CreatedOnToolsVersion = 16.4;
					};
					B89BD8382DFC4F2A002E7017 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = B89BD82B2DFC4F29002E7017;
					};
					B89BD8422DFC4F2A002E7017 = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = B89BD82B2DFC4F29002E7017;
					};
				};
			};
			buildConfigurationList = B89BD8272DFC4F29002E7017 /* Build configuration list for PBXProject "Dash" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = B89BD8232DFC4F29002E7017;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				B80A9B702E3AD5660048AA71 /* XCRemoteSwiftPackageReference "stripe-ios" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = B89BD82D2DFC4F29002E7017 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				B89BD82B2DFC4F29002E7017 /* Dash */,
				B89BD8382DFC4F2A002E7017 /* DashTests */,
				B89BD8422DFC4F2A002E7017 /* DashUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		B89BD82A2DFC4F29002E7017 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B89BD86C2DFC5330002E7017 /* GoogleService-Info.plist in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B89BD8372DFC4F2A002E7017 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B89BD8412DFC4F2A002E7017 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		5B7DB9648C0AD75472123396 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Dash/Pods-Dash-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			inputPaths = (
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Dash/Pods-Dash-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Dash/Pods-Dash-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		5E681D3D17CFD29D6C2A81B4 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Dash-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		B89BD8282DFC4F29002E7017 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B89BD8352DFC4F2A002E7017 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B89BD83F2DFC4F2A002E7017 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		B89BD83B2DFC4F2A002E7017 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B89BD82B2DFC4F29002E7017 /* Dash */;
			targetProxy = B89BD83A2DFC4F2A002E7017 /* PBXContainerItemProxy */;
		};
		B89BD8452DFC4F2A002E7017 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B89BD82B2DFC4F29002E7017 /* Dash */;
			targetProxy = B89BD8442DFC4F2A002E7017 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		B89BD84B2DFC4F2A002E7017 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = S27679AD87;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		B89BD84C2DFC4F2A002E7017 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = S27679AD87;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = NO;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		B89BD84E2DFC4F2A002E7017 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DCD4286E7D3A630910667D5D /* Pods-Dash.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = S27679AD87;
				ENABLE_PREVIEWS = YES;
				"FRAMEWORK_SEARCH_PATHS[arch=*]" = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/AppAuth\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/AppCheckCore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/BoringSSL-GRPC\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Firebase\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAppCheckInterop\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAuth\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAuthInterop\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreExtension\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseFirestore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseFirestoreInternal\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseSharedSwift\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GTMAppAuth\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GTMSessionFetcher\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GoogleSignIn\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RecaptchaInterop\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/abseil\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/gRPC-C++\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/gRPC-Core\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/leveldb-library\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/nanopb\"",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Dash/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.schwartzapps.Dash;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		B89BD84F2DFC4F2A002E7017 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B7D3F39C16D0D07DB58269FB /* Pods-Dash.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = S27679AD87;
				ENABLE_PREVIEWS = YES;
				"FRAMEWORK_SEARCH_PATHS[arch=*]" = (
					"$(inherited)",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/AppAuth\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/AppCheckCore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/BoringSSL-GRPC\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/Firebase\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAppCheckInterop\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAuth\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseAuthInterop\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreExtension\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseCoreInternal\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseFirestore\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseFirestoreInternal\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/FirebaseSharedSwift\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GTMAppAuth\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GTMSessionFetcher\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GoogleSignIn\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/GoogleUtilities\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/PromisesObjC\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/RecaptchaInterop\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/abseil\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/gRPC-C++\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/gRPC-Core\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/leveldb-library\"",
					"\"${PODS_CONFIGURATION_BUILD_DIR}/nanopb\"",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = Dash/Info.plist;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				ONLY_ACTIVE_ARCH = NO;
				PRODUCT_BUNDLE_IDENTIFIER = com.schwartzapps.Dash;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		B89BD8512DFC4F2A002E7017 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = S27679AD87;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.schwartzapps.DashTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Dash.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Dash";
			};
			name = Debug;
		};
		B89BD8522DFC4F2A002E7017 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = S27679AD87;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.schwartzapps.DashTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Dash.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Dash";
			};
			name = Release;
		};
		B89BD8542DFC4F2A002E7017 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = S27679AD87;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.schwartzapps.DashUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Dash;
			};
			name = Debug;
		};
		B89BD8552DFC4F2A002E7017 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = S27679AD87;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.schwartzapps.DashUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = Dash;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		B89BD8272DFC4F29002E7017 /* Build configuration list for PBXProject "Dash" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B89BD84B2DFC4F2A002E7017 /* Debug */,
				B89BD84C2DFC4F2A002E7017 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B89BD84D2DFC4F2A002E7017 /* Build configuration list for PBXNativeTarget "Dash" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B89BD84E2DFC4F2A002E7017 /* Debug */,
				B89BD84F2DFC4F2A002E7017 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B89BD8502DFC4F2A002E7017 /* Build configuration list for PBXNativeTarget "DashTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B89BD8512DFC4F2A002E7017 /* Debug */,
				B89BD8522DFC4F2A002E7017 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B89BD8532DFC4F2A002E7017 /* Build configuration list for PBXNativeTarget "DashUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B89BD8542DFC4F2A002E7017 /* Debug */,
				B89BD8552DFC4F2A002E7017 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		B80A9B702E3AD5660048AA71 /* XCRemoteSwiftPackageReference "stripe-ios" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/stripe/stripe-ios";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 24.18.1;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		B80A9B712E3AD5660048AA71 /* Stripe */ = {
			isa = XCSwiftPackageProductDependency;
			package = B80A9B702E3AD5660048AA71 /* XCRemoteSwiftPackageReference "stripe-ios" */;
			productName = Stripe;
		};
		B80A9B732E3AD5660048AA71 /* StripeApplePay */ = {
			isa = XCSwiftPackageProductDependency;
			package = B80A9B702E3AD5660048AA71 /* XCRemoteSwiftPackageReference "stripe-ios" */;
			productName = StripeApplePay;
		};
		B80A9B752E3AD5660048AA71 /* StripeCardScan */ = {
			isa = XCSwiftPackageProductDependency;
			package = B80A9B702E3AD5660048AA71 /* XCRemoteSwiftPackageReference "stripe-ios" */;
			productName = StripeCardScan;
		};
		B80A9B772E3AD5660048AA71 /* StripeConnect */ = {
			isa = XCSwiftPackageProductDependency;
			package = B80A9B702E3AD5660048AA71 /* XCRemoteSwiftPackageReference "stripe-ios" */;
			productName = StripeConnect;
		};
		B80A9B792E3AD5660048AA71 /* StripeFinancialConnections */ = {
			isa = XCSwiftPackageProductDependency;
			package = B80A9B702E3AD5660048AA71 /* XCRemoteSwiftPackageReference "stripe-ios" */;
			productName = StripeFinancialConnections;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = B89BD8242DFC4F29002E7017 /* Project object */;
}
