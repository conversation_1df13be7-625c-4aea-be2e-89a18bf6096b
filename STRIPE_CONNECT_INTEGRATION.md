# Stripe Connect Integration Guide for Dash

This guide explains how to complete the Stripe Connect integration for the Dash finance app with pooled funds architecture.

## Overview

The Dash app now implements Stripe Connect with Express accounts to enable splits functionality using a pooled funds approach. Here's how it works:

1. **User deposits money** → Firebase balance update, money goes into Dash account (pooled funds)
2. **User balance goes down** → Split creator's balance goes up after split actioned
3. **Settlement process** → Transfers funds to participants using Stripe Connect

## Architecture

### Pooled Funds Model

- Platform (Dash) holds all funds in a single Stripe account
- Individual user balances are tracked in Firebase Firestore
- Stripe Connect Express accounts are used for identity verification and compliance
- Destination charges are used to transfer funds between users

### Key Components

1. **StripeConnectService.swift** - Handles Express account creation and onboarding
2. **StripeConnectPaymentService.swift** - Processes split payments and settlements
3. **StripeConnectOnboardingView.swift** - User onboarding flow
4. **PaymentMethodSetupView.swift** - Payment method collection
5. **IdentityVerificationView.swift** - Identity verification using Stripe Identity
6. **Backend endpoints** - Express account management and payment processing

## Setup Instructions

### 1. Stripe Dashboard Configuration

1. Create a Stripe Connect platform account
2. Enable Express accounts in your Stripe dashboard
3. Configure webhook endpoints for account status updates
4. Get your publishable and secret keys

### 2. Backend Configuration

The backend uses environment variables for Stripe configuration. The `.env` file has been created with your keys:

```bash
STRIPE_SECRET_KEY=sk_test_51RqXNVEGqgdjTeexVTQfGwkq2bT3S6RwBaPRUB5Ldow2iYsUtT6gmzWwpwXZnXHPbTZVzfrqlSLQ9rk08v4MTC7200Epk5Vdcs
STRIPE_PUBLISHABLE_KEY=pk_test_51RqXNVEGqgdjTeexCgsafo9ysyjHjFVMnKCyOs8yIZ7YVlvcAFVJHPqQcgbXw6z8sJXNAIJ4RdnoJTgqdE9pP7f3003TzW4qpv
```

### 3. iOS App Configuration

The iOS app has been configured with your Stripe publishable key in `Dash/AppDelegate.swift`:

```swift
StripeAPI.defaultPublishableKey = "pk_test_51RqXNVEGqgdjTeexCgsafo9ysyjHjFVMnKCyOs8yIZ7YVlvcAFVJHPqQcgbXw6z8sJXNAIJ4RdnoJTgqdE9pP7f3003TzW4qpv"
```

### 4. Required Dependencies

Add these to your iOS project:

- Stripe iOS SDK
- StripePaymentSheet
- StripeIdentity (for identity verification)

Add these to your backend:

- stripe (Node.js package)
- express
- firebase-admin

## User Flow

### 1. User Onboarding

1. User signs up for Dash
2. **StripeConnectOnboardingView** guides through Express account setup
3. **PaymentMethodSetupView** collects payment method
4. **IdentityVerificationView** handles KYC verification
5. User can now participate in splits

### 2. Adding Money

1. User selects "Add Money"
2. **StripeConnectPaymentService.addMoneyToAccount()** processes payment
3. Money goes to Dash platform account (pooled funds)
4. User's Firebase balance is updated

### 3. Split Creation & Payment

1. Creator creates split
2. Participants join split
3. **StripeConnectPaymentService.processSplitPayment()** handles payments
4. Uses destination charges to transfer funds
5. Firebase balances are updated accordingly

### 4. Settlement

1. Creator initiates settlement
2. **StripeConnectPaymentService.settleSplit()** transfers funds to participants
3. Settlement transactions are recorded
4. Participant balances are updated

## API Endpoints

### Express Account Management

- `POST /stripe/create-account` - Create Express account
- `POST /stripe/create-account-link` - Generate onboarding link
- `GET /stripe/account-status/:accountId` - Check account status

### Payment Processing

- `POST /stripe/create-split-payment` - Process split payment
- `POST /stripe/settle-split` - Settle split with transfers
- `POST /stripe/add-money` - Add money to user account

### Webhooks

- `POST /stripe/webhook` - Handle Stripe webhook events

## Data Models

### User Model Updates

```swift
// Stripe Connect Fields
var stripeAccountId: String?
var stripeAccountStatus: String = "not_started"
var stripePaymentMethodId: String?
var canJoinSplits: Bool = false
var identityVerified: Bool = false
```

### Split Model Updates

```swift
// Stripe Connect fields
var stripePaymentIntentId: String?
var stripeTransferIds: [String: String]
var paymentProcessedAt: Date?
```

### Transaction Model Updates

```swift
// Stripe Connect fields
var stripePaymentIntentId: String?
var stripeTransferId: String?
var stripeChargeId: String?

// New transaction types
case addMoney = "add_money"
case settlement = "settlement"
case splitPayment = "split_payment"
case stripeTransfer = "stripe_transfer"
```

## Security Considerations

1. **Firebase Authentication** - All API calls require valid Firebase ID tokens
2. **User Verification** - Identity verification required before joining splits
3. **Payment Method Security** - Stripe handles PCI compliance
4. **Webhook Verification** - Stripe webhook signatures are verified
5. **Balance Validation** - Server-side balance checks prevent overdrafts

## Testing

### Test Cards

Use Stripe test cards for development:

- `****************` - Visa (succeeds)
- `****************` - Visa (declined)
- `****************` - Visa (insufficient funds)

### Test Flow

1. Create test users with different Stripe Connect statuses
2. Test onboarding flow with test accounts
3. Verify payment processing with test cards
4. Test settlement process with multiple participants

## Production Deployment

### 1. Stripe Configuration

- Switch to live Stripe keys
- Configure production webhook endpoints
- Set up proper domain verification

### 2. Firebase Configuration

- Update Firebase security rules
- Configure production Firestore indexes
- Set up proper backup strategies

### 3. Compliance

- Ensure proper KYC/AML compliance
- Review Stripe Connect terms of service
- Implement proper user agreement flows

## Troubleshooting

### Common Issues

1. **Account creation fails** - Check Stripe Connect settings
2. **Onboarding link expired** - Generate new link
3. **Payment method setup fails** - Verify Stripe publishable key
4. **Identity verification stuck** - Check Stripe Identity configuration

### Debug Tools

- Stripe Dashboard logs
- Firebase Console logs
- iOS Console for client-side debugging
- Backend server logs

## Next Steps

1. **Test thoroughly** with various user scenarios
2. **Implement error handling** for edge cases
3. **Add monitoring** for payment failures
4. **Set up alerts** for webhook failures
5. **Review compliance** requirements for your jurisdiction

## Support

For Stripe-specific issues:

- Stripe Documentation: https://stripe.com/docs/connect
- Stripe Support: https://support.stripe.com

For Firebase issues:

- Firebase Documentation: https://firebase.google.com/docs
- Firebase Support: https://firebase.google.com/support
