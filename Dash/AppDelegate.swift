import UIKit
import Firebase
import UserNotifications
import Stripe
import GoogleSignIn

class AppDelegate: NSObject, UIApplicationDelegate {
    func application(_ application: UIApplication,
                     didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey : Any]? = nil) -> Bool {
        // Configure Firebase
        FirebaseApp.configure()

        // Configure Google Sign-In
        guard let path = Bundle.main.path(forResource: "GoogleService-Info", ofType: "plist"),
              let plist = NSDictionary(contentsOfFile: path),
              let clientId = plist["CLIENT_ID"] as? String else {
            fatalError("Couldn't get CLIENT_ID from GoogleService-Info.plist")
        }

        GIDSignIn.sharedInstance.configuration = GIDConfiguration(clientID: clientId)

        // Configure Stripe
        StripeAPI.defaultPublishableKey = "pk_test_51RqXNVEGqgdjTeexCgsafo9ysyjHjFVMnKCyOs8yIZ7YVlvcAFVJHPqQcgbXw6z8sJXNAIJ4RdnoJTgqdE9pP7f3003TzW4qpv"

        // Set up local notifications
        UNUserNotificationCenter.current().delegate = NotificationService.shared

        return true
    }
}
