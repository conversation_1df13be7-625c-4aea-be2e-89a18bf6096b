import Foundation
import Combine
import Firebase
import FirebaseAuth

@MainActor
class StripeConnectPaymentService: ObservableObject {
    static let shared = StripeConnectPaymentService()
    
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var successMessage: String?
    
    private let baseURL = "http://localhost:3000" // Update with your backend URL
    private var cancellables = Set<AnyCancellable>()
    private let db = Firestore.firestore()
    
    private init() {}
    
    // MARK: - Split Payment Processing
    
    /// Process payment for a split using Stripe Connect with pooled funds
    func processSplitPayment(split: Split, payingUserId: String, amount: Double) async throws -> String {
        guard let splitId = split.id else {
            throw StripePaymentError.invalidSplit
        }
        
        isLoading = true
        errorMessage = nil
        
        do {
            let paymentData = CreateSplitPaymentRequest(
                splitId: splitId,
                payingUserId: payingUserId,
                amount: amount,
                creatorAccountId: split.creatorId
            )
            
            let paymentIntentId = try await performCreateSplitPayment(paymentData)
            
            // Update split with payment intent ID
            try await updateSplitWithPaymentIntent(splitId: splitId, paymentIntentId: paymentIntentId)
            
            // Update user's Firebase balance (deduct amount)
            try await updateUserBalance(userId: payingUserId, amount: -amount)
            
            // Update split creator's Firebase balance (add amount)
            try await updateUserBalance(userId: split.creatorId, amount: amount)
            
            // Record the payment in the split
            try await recordSplitPayment(splitId: splitId, userId: payingUserId, amount: amount)
            
            isLoading = false
            successMessage = "Payment processed successfully"
            return paymentIntentId
            
        } catch {
            isLoading = false
            errorMessage = error.localizedDescription
            throw error
        }
    }
    
    /// Settle a split by transferring funds to participants
    func settleSplit(split: Split) async throws {
        guard let splitId = split.id else {
            throw StripePaymentError.invalidSplit
        }
        
        guard split.status == .complete else {
            throw StripePaymentError.splitNotComplete
        }
        
        isLoading = true
        errorMessage = nil
        
        do {
            let settlementData = SettleSplitRequest(
                splitId: splitId,
                creatorId: split.creatorId,
                participants: split.participants,
                totalAmount: split.totalAmount
            )
            
            let transferIds = try await performSettleSplit(settlementData)
            
            // Update split with transfer IDs
            try await updateSplitWithTransfers(splitId: splitId, transferIds: transferIds)
            
            // Process settlements for each participant
            for participant in split.participants {
                let amountOwed = split.totalAmount / Double(split.numberOfParticipants)
                
                // Deduct from participant's Firebase balance
                try await updateUserBalance(userId: participant.userId, amount: -amountOwed)
                
                // Add settlement transaction to participant's history
                try await createSettlementTransaction(
                    fromUserId: participant.userId,
                    toUserId: split.creatorId,
                    amount: amountOwed,
                    splitId: splitId
                )
            }
            
            isLoading = false
            successMessage = "Split settled successfully"
            
        } catch {
            isLoading = false
            errorMessage = error.localizedDescription
            throw error
        }
    }
    
    /// Add money to user's account using Stripe Connect
    func addMoneyToAccount(userId: String, amount: Double, paymentMethodId: String) async throws -> String {
        isLoading = true
        errorMessage = nil
        
        do {
            let addMoneyData = AddMoneyRequest(
                userId: userId,
                amount: amount,
                paymentMethodId: paymentMethodId
            )
            
            let paymentIntentId = try await performAddMoney(addMoneyData)
            
            // Update user's Firebase balance
            try await updateUserBalance(userId: userId, amount: amount)
            
            // Create transaction record
            try await createAddMoneyTransaction(userId: userId, amount: amount, paymentIntentId: paymentIntentId)
            
            isLoading = false
            successMessage = "Money added successfully"
            return paymentIntentId
            
        } catch {
            isLoading = false
            errorMessage = error.localizedDescription
            throw error
        }
    }
    
    // MARK: - Private Network Methods
    
    private func performCreateSplitPayment(_ data: CreateSplitPaymentRequest) async throws -> String {
        guard let url = URL(string: "\(baseURL)/stripe/create-split-payment") else {
            throw StripePaymentError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        if let idToken = try? await Auth.auth().currentUser?.getIDToken() {
            request.setValue("Bearer \(idToken)", forHTTPHeaderField: "Authorization")
        }
        
        request.httpBody = try JSONEncoder().encode(data)
        
        let (responseData, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw StripePaymentError.networkError
        }
        
        let result = try JSONDecoder().decode(PaymentIntentResponse.self, from: responseData)
        return result.paymentIntentId
    }
    
    private func performSettleSplit(_ data: SettleSplitRequest) async throws -> [String: String] {
        guard let url = URL(string: "\(baseURL)/stripe/settle-split") else {
            throw StripePaymentError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        if let idToken = try? await Auth.auth().currentUser?.getIDToken() {
            request.setValue("Bearer \(idToken)", forHTTPHeaderField: "Authorization")
        }
        
        request.httpBody = try JSONEncoder().encode(data)
        
        let (responseData, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw StripePaymentError.networkError
        }
        
        let result = try JSONDecoder().decode(SettlementResponse.self, from: responseData)
        return result.transferIds
    }
    
    private func performAddMoney(_ data: AddMoneyRequest) async throws -> String {
        guard let url = URL(string: "\(baseURL)/stripe/add-money") else {
            throw StripePaymentError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        if let idToken = try? await Auth.auth().currentUser?.getIDToken() {
            request.setValue("Bearer \(idToken)", forHTTPHeaderField: "Authorization")
        }
        
        request.httpBody = try JSONEncoder().encode(data)
        
        let (responseData, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw StripePaymentError.networkError
        }
        
        let result = try JSONDecoder().decode(PaymentIntentResponse.self, from: responseData)
        return result.paymentIntentId
    }
    
    // MARK: - Firebase Updates
    
    private func updateUserBalance(userId: String, amount: Double) async throws {
        let userRef = db.collection("users").document(userId)
        
        try await db.runTransaction { transaction, errorPointer in
            let userDocument: DocumentSnapshot
            do {
                try userDocument = transaction.getDocument(userRef)
            } catch let fetchError as NSError {
                errorPointer?.pointee = fetchError
                return nil
            }
            
            let oldBalance = userDocument.data()?["balance"] as? Double ?? 0.0
            let newBalance = oldBalance + amount
            
            transaction.updateData(["balance": newBalance], forDocument: userRef)
            return nil
        }
    }
    
    private func updateSplitWithPaymentIntent(splitId: String, paymentIntentId: String) async throws {
        let splitRef = db.collection("splits").document(splitId)
        
        try await splitRef.updateData([
            "stripePaymentIntentId": paymentIntentId,
            "paymentProcessedAt": Timestamp(date: Date())
        ])
    }
    
    private func updateSplitWithTransfers(splitId: String, transferIds: [String: String]) async throws {
        let splitRef = db.collection("splits").document(splitId)
        
        try await splitRef.updateData([
            "stripeTransferIds": transferIds,
            "status": SplitStatus.complete.rawValue
        ])
    }
    
    private func recordSplitPayment(splitId: String, userId: String, amount: Double) async throws {
        let splitRef = db.collection("splits").document(splitId)
        
        try await splitRef.updateData([
            "paidParticipants.\(userId)": amount
        ])
    }
    
    private func createSettlementTransaction(fromUserId: String, toUserId: String, amount: Double, splitId: String) async throws {
        let transactionData: [String: Any] = [
            "fromUserId": fromUserId,
            "toUserId": toUserId,
            "amount": amount,
            "type": "settlement",
            "splitId": splitId,
            "timestamp": Timestamp(date: Date()),
            "status": "completed"
        ]
        
        try await db.collection("transactions").addDocument(data: transactionData)
    }
    
    private func createAddMoneyTransaction(userId: String, amount: Double, paymentIntentId: String) async throws {
        let transactionData: [String: Any] = [
            "userId": userId,
            "amount": amount,
            "type": "add_money",
            "stripePaymentIntentId": paymentIntentId,
            "timestamp": Timestamp(date: Date()),
            "status": "completed"
        ]
        
        try await db.collection("transactions").addDocument(data: transactionData)
    }
}

// MARK: - Data Models

struct CreateSplitPaymentRequest: Codable {
    let splitId: String
    let payingUserId: String
    let amount: Double
    let creatorAccountId: String
}

struct SettleSplitRequest: Codable {
    let splitId: String
    let creatorId: String
    let participants: [Participant]
    let totalAmount: Double
}

struct AddMoneyRequest: Codable {
    let userId: String
    let amount: Double
    let paymentMethodId: String
}

struct PaymentIntentResponse: Codable {
    let paymentIntentId: String
    let success: Bool
}

struct SettlementResponse: Codable {
    let transferIds: [String: String]
    let success: Bool
}

// MARK: - Errors

enum StripePaymentError: LocalizedError {
    case invalidSplit
    case splitNotComplete
    case invalidURL
    case networkError
    case paymentFailed
    case settlementFailed
    
    var errorDescription: String? {
        switch self {
        case .invalidSplit:
            return "Invalid split information"
        case .splitNotComplete:
            return "Split is not ready for settlement"
        case .invalidURL:
            return "Invalid server URL"
        case .networkError:
            return "Network connection failed"
        case .paymentFailed:
            return "Payment processing failed"
        case .settlementFailed:
            return "Settlement processing failed"
        }
    }
}
