import Foundation
import Combine
import Firebase
import Firebase<PERSON>uth

@MainActor
class StripeConnectService: ObservableObject {
    static let shared = StripeConnectService()
    
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var successMessage: String?
    @Published var accountStatus: StripeAccountStatus = .notStarted
    
    private let baseURL = "http://localhost:3000" // Update with your backend URL
    private var cancellables = Set<AnyCancellable>()
    private let db = Firestore.firestore()
    
    private init() {}
    
    // MARK: - Account Creation and Onboarding
    
    /// Create a Stripe Express connected account for the user
    func createConnectedAccount(for user: User) async throws -> String {
        guard let userId = user.uid else {
            throw StripeConnectError.invalidUser
        }
        
        isLoading = true
        errorMessage = nil
        
        do {
            let accountData = CreateAccountRequest(
                userId: userId,
                email: user.email,
                displayName: user.displayName,
                phoneNumber: user.phoneNumber
            )
            
            let accountId = try await performCreateAccount(accountData)
            
            // Update user document with Stripe account ID
            try await updateUserStripeAccount(userId: userId, accountId: accountId, status: .pendingOnboarding)
            
            isLoading = false
            return accountId
            
        } catch {
            isLoading = false
            errorMessage = error.localizedDescription
            throw error
        }
    }
    
    /// Create an account link for onboarding
    func createAccountLink(accountId: String, userId: String) async throws -> String {
        isLoading = true
        errorMessage = nil
        
        do {
            let linkData = CreateAccountLinkRequest(
                accountId: accountId,
                userId: userId,
                returnUrl: "dash://onboarding-complete",
                refreshUrl: "dash://onboarding-refresh"
            )
            
            let linkUrl = try await performCreateAccountLink(linkData)
            isLoading = false
            return linkUrl
            
        } catch {
            isLoading = false
            errorMessage = error.localizedDescription
            throw error
        }
    }
    
    /// Check account status and update local state
    func checkAccountStatus(accountId: String) async throws -> StripeAccountStatus {
        do {
            let status = try await performAccountStatusCheck(accountId)
            await MainActor.run {
                self.accountStatus = status
            }
            return status
        } catch {
            errorMessage = error.localizedDescription
            throw error
        }
    }
    
    // MARK: - Payment Methods
    
    /// Setup payment method for user
    func setupPaymentMethod(for userId: String) async throws -> String {
        isLoading = true
        errorMessage = nil
        
        do {
            let setupIntentData = CreateSetupIntentRequest(userId: userId)
            let clientSecret = try await performCreateSetupIntent(setupIntentData)
            isLoading = false
            return clientSecret
            
        } catch {
            isLoading = false
            errorMessage = error.localizedDescription
            throw error
        }
    }
    
    /// Confirm payment method setup
    func confirmPaymentMethodSetup(userId: String, paymentMethodId: String) async throws {
        do {
            try await updateUserPaymentMethod(userId: userId, paymentMethodId: paymentMethodId)
            successMessage = "Payment method added successfully"
        } catch {
            errorMessage = error.localizedDescription
            throw error
        }
    }
    
    // MARK: - Private Network Methods
    
    private func performCreateAccount(_ data: CreateAccountRequest) async throws -> String {
        guard let url = URL(string: "\(baseURL)/stripe/create-account") else {
            throw StripeConnectError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // Add Firebase ID token for authentication
        if let idToken = try? await Auth.auth().currentUser?.getIDToken() {
            request.setValue("Bearer \(idToken)", forHTTPHeaderField: "Authorization")
        }
        
        request.httpBody = try JSONEncoder().encode(data)
        
        let (responseData, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw StripeConnectError.networkError
        }
        
        let result = try JSONDecoder().decode(CreateAccountResponse.self, from: responseData)
        return result.accountId
    }
    
    private func performCreateAccountLink(_ data: CreateAccountLinkRequest) async throws -> String {
        guard let url = URL(string: "\(baseURL)/stripe/create-account-link") else {
            throw StripeConnectError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        if let idToken = try? await Auth.auth().currentUser?.getIDToken() {
            request.setValue("Bearer \(idToken)", forHTTPHeaderField: "Authorization")
        }
        
        request.httpBody = try JSONEncoder().encode(data)
        
        let (responseData, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw StripeConnectError.networkError
        }
        
        let result = try JSONDecoder().decode(CreateAccountLinkResponse.self, from: responseData)
        return result.url
    }
    
    private func performAccountStatusCheck(_ accountId: String) async throws -> StripeAccountStatus {
        guard let url = URL(string: "\(baseURL)/stripe/account-status/\(accountId)") else {
            throw StripeConnectError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        
        if let idToken = try? await Auth.auth().currentUser?.getIDToken() {
            request.setValue("Bearer \(idToken)", forHTTPHeaderField: "Authorization")
        }
        
        let (responseData, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw StripeConnectError.networkError
        }
        
        let result = try JSONDecoder().decode(AccountStatusResponse.self, from: responseData)
        return StripeAccountStatus(from: result.status)
    }
    
    private func performCreateSetupIntent(_ data: CreateSetupIntentRequest) async throws -> String {
        guard let url = URL(string: "\(baseURL)/stripe/create-setup-intent") else {
            throw StripeConnectError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        if let idToken = try? await Auth.auth().currentUser?.getIDToken() {
            request.setValue("Bearer \(idToken)", forHTTPHeaderField: "Authorization")
        }
        
        request.httpBody = try JSONEncoder().encode(data)
        
        let (responseData, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw StripeConnectError.networkError
        }
        
        let result = try JSONDecoder().decode(SetupIntentResponse.self, from: responseData)
        return result.clientSecret
    }
    
    // MARK: - Firebase Updates
    
    private func updateUserStripeAccount(userId: String, accountId: String, status: StripeAccountStatus) async throws {
        let userRef = db.collection("users").document(userId)
        
        try await userRef.updateData([
            "stripeAccountId": accountId,
            "stripeAccountStatus": status.rawValue,
            "stripeAccountCreatedAt": Timestamp(date: Date())
        ])
    }
    
    private func updateUserPaymentMethod(userId: String, paymentMethodId: String) async throws {
        let userRef = db.collection("users").document(userId)
        
        try await userRef.updateData([
            "stripePaymentMethodId": paymentMethodId,
            "paymentMethodSetupAt": Timestamp(date: Date()),
            "canJoinSplits": true
        ])
    }
}

// MARK: - Data Models

struct CreateAccountRequest: Codable {
    let userId: String
    let email: String?
    let displayName: String?
    let phoneNumber: String?
}

struct CreateAccountResponse: Codable {
    let accountId: String
    let success: Bool
}

struct CreateAccountLinkRequest: Codable {
    let accountId: String
    let userId: String
    let returnUrl: String
    let refreshUrl: String
}

struct CreateAccountLinkResponse: Codable {
    let url: String
    let success: Bool
}

struct CreateSetupIntentRequest: Codable {
    let userId: String
}

struct SetupIntentResponse: Codable {
    let clientSecret: String
    let success: Bool
}

struct AccountStatusResponse: Codable {
    let status: String
    let chargesEnabled: Bool
    let detailsSubmitted: Bool
    let success: Bool
}

// MARK: - Enums and Errors

enum StripeAccountStatus: String, Codable, CaseIterable {
    case notStarted = "not_started"
    case pendingOnboarding = "pending_onboarding"
    case pendingVerification = "pending_verification"
    case active = "active"
    case rejected = "rejected"
    case restricted = "restricted"
    
    init(from statusString: String) {
        switch statusString {
        case "active": self = .active
        case "pending_verification": self = .pendingVerification
        case "pending_onboarding": self = .pendingOnboarding
        case "rejected": self = .rejected
        case "restricted": self = .restricted
        default: self = .notStarted
        }
    }
    
    var displayName: String {
        switch self {
        case .notStarted: return "Not Started"
        case .pendingOnboarding: return "Pending Onboarding"
        case .pendingVerification: return "Pending Verification"
        case .active: return "Active"
        case .rejected: return "Rejected"
        case .restricted: return "Restricted"
        }
    }
    
    var canAcceptPayments: Bool {
        return self == .active
    }
}

enum StripeConnectError: LocalizedError {
    case invalidUser
    case invalidURL
    case networkError
    case accountCreationFailed
    case onboardingFailed
    case paymentMethodSetupFailed
    
    var errorDescription: String? {
        switch self {
        case .invalidUser:
            return "Invalid user information"
        case .invalidURL:
            return "Invalid server URL"
        case .networkError:
            return "Network connection failed"
        case .accountCreationFailed:
            return "Failed to create Stripe account"
        case .onboardingFailed:
            return "Onboarding process failed"
        case .paymentMethodSetupFailed:
            return "Failed to setup payment method"
        }
    }
}
