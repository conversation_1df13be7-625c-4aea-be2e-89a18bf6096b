//
//  DashApp.swift
//  Dash
//
//  Created by <PERSON> on 13/6/2025.
//

import SwiftUI
import Firebase
import GoogleSignIn
import Stripe

@main
struct DashApp: App {
    @UIApplicationDelegateAdaptor(AppDelegate.self) var delegate
    @StateObject private var authViewModel = AuthViewModel.shared
    @StateObject private var securityViewModel = SecurityViewModel()
    @StateObject private var notificationService = NotificationService.shared
    @AppStorage("isDarkMode") private var isDarkMode = false
    @Environment(\.scenePhase) private var scenePhase

    var body: some Scene {
        WindowGroup {
            Group {
                if authViewModel.userSession == nil || authViewModel.authenticationState == .unauthenticated {
                    WelcomeView()
                        .environmentObject(authViewModel)
                } else if authViewModel.authenticationState == .pendingEmailVerification {
                    EmailVerificationView()
                        .environmentObject(authViewModel)
                } else if authViewModel.authenticationState == .pendingTwoFactor {
                    TwoFactorVerificationView()
                        .environmentObject(authViewModel)
                } else {
                    // User is authenticated, check PIN and security
                    if securityViewModel.isLockedOut {
                        // User is locked out, show welcome screen (will trigger logout)
                        WelcomeView()
                            .environmentObject(authViewModel)
                            .onAppear {
                                // Ensure user is signed out when locked out
                                if authViewModel.userSession != nil {
                                    authViewModel.signOut()
                                }
                            }
                    } else if securityViewModel.isPinSet && !securityViewModel.requiresPinReset {
                        if securityViewModel.isAuthenticated && !securityViewModel.requiresReauthentication {
                            DashboardView()
                                .environmentObject(authViewModel)
                                .environmentObject(securityViewModel)
                                .environmentObject(notificationService)
                                .onAppear(perform: securityViewModel.checkPinStatus)
                        } else {
                            LockScreenView()
                                .environmentObject(securityViewModel)
                        }
                    } else {
                        PINSetupView()
                            .environmentObject(securityViewModel)
                            .onAppear {
                                print("DEBUG: Showing PINSetupView - isPinSet: \(securityViewModel.isPinSet), requiresPinReset: \(securityViewModel.requiresPinReset)")
                            }
                    }
                }
            }
            .preferredColorScheme(isDarkMode ? .dark : .light)
            .onReceive(authViewModel.$userSession) { userSession in
                // When the user session changes (login/logout), re-check the PIN status.
                securityViewModel.checkPinStatus()
                // If the user logs out, reset the authentication state.
                if userSession == nil {
                    securityViewModel.logout()
                    authViewModel.authenticationState = .unauthenticated
                } else {
                    // User logged in, check authentication state
                    authViewModel.checkAuthenticationState()
                    // Only reset security authentication state if not already authenticated
                    // This prevents resetting after successful Face ID authentication
                    if !securityViewModel.isAuthenticated {
                        securityViewModel.resetAuthenticationState()
                    }
                    // Request notification permission
                    Task {
                        await notificationService.requestPermission()
                    }
                }
            }
            .onReceive(authViewModel.$currentUser) { user in
                // When user data is loaded, check authentication state
                authViewModel.checkAuthenticationState()
            }
            .onChange(of: scenePhase) { _, newPhase in
                handleScenePhaseChange(newPhase)
            }
            .onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)) { _ in
                // When app comes to foreground, check for new notifications
                print("DashApp: App entering foreground, checking for new notifications")
                NotificationService.shared.checkForNewNotifications()
            }
            .onOpenURL { url in
                // Handle Google Sign In URLs
                if url.scheme == "com.googleusercontent.apps.46095870775-lbm74us5t9evk4g3objvamfes6sre05k" {
                    GIDSignIn.sharedInstance.handle(url)
                }
                // Handle Dash app deep links
                else if url.scheme == "dash" {
                    handleDashURL(url)
                }
            }
        }
    }
    
    private func handleScenePhaseChange(_ phase: ScenePhase) {
        switch phase {
        case .background:
            // App went to background, only reset PIN/biometric authentication
            // Don't reset 2FA authentication state - that should only happen on explicit sign out
            print("DEBUG: App went to background")

            // Stop the notification listener to save battery and resources
            notificationService.stopNotificationListener()

            // Start aggressive notification checking for when app is backgrounded
            notificationService.startAggressiveNotificationChecking()

            if authViewModel.userSession != nil {
                securityViewModel.resetAuthenticationState()
                // Remove the line that resets 2FA authentication state
                // authViewModel.resetAuthenticationState() // This was causing 2FA to be required on every app close
            }
        case .active:
            // App became active, check if PIN re-authentication is needed
            // Don't force 2FA re-authentication unless explicitly signed out
            print("DEBUG: App became active")
            print("DEBUG: userSession != nil: \(authViewModel.userSession != nil)")
            print("DEBUG: authenticationState: \(authViewModel.authenticationState)")
            print("DEBUG: isPinSet: \(securityViewModel.isPinSet)")
            print("DEBUG: isAuthenticated: \(securityViewModel.isAuthenticated)")
            print("DEBUG: requiresReauthentication: \(securityViewModel.requiresReauthentication)")

            // Stop scheduled checks and restart real-time listener
            notificationService.stopScheduledNotificationChecks()
            notificationService.restartNotificationListener()
            notificationService.checkForNewNotifications()

            if authViewModel.userSession != nil &&
               authViewModel.authenticationState == .authenticated &&
               securityViewModel.isPinSet &&
               !securityViewModel.isAuthenticated &&
               !securityViewModel.requiresReauthentication {
                // User is logged in, has PIN set, but not authenticated - trigger re-authentication
                print("DEBUG: App became active, triggering re-authentication")
                securityViewModel.requireReauthentication()
            }
            break
        case .inactive:
            // App became inactive (e.g., during phone calls)
            break
        @unknown default:
            break
        }
    }

    private func handleDashURL(_ url: URL) {
        print("DashApp: Handling deep link: \(url)")

        // Handle pod invitation links: dash://join/INVITE_CODE
        if url.host == "join" {
            let inviteCode = String(url.path.dropFirst()) // Remove leading "/"
            print("DashApp: Detected pod invitation code from deep link: \(inviteCode)")

            // Post notification to handle the invite code
            NotificationCenter.default.post(
                name: NSNotification.Name("HandlePodInvite"),
                object: nil,
                userInfo: ["inviteCode": inviteCode]
            )
        }
    }
}
