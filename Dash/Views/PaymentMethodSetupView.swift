import SwiftUI
import Stripe
import StripePaymentSheet

struct PaymentMethodSetupView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var stripeConnectService = StripeConnectService.shared
    
    @State private var paymentSheet: PaymentSheet?
    @State private var paymentResult: PaymentSheetResult?
    @State private var isSetupComplete = false
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.theme.background
                    .ignoresSafeArea()
                
                VStack(spacing: Spacing.xl) {
                    // Header
                    VStack(spacing: Spacing.md) {
                        Image(systemName: "creditcard.circle.fill")
                            .font(.system(size: 60, weight: .light))
                            .foregroundColor(Color.theme.accent)
                        
                        Text("Add Payment Method")
                            .font(Font.theme.headlineLarge)
                            .fontWeight(.bold)
                            .foregroundColor(Color.theme.textPrimary)
                            .multilineTextAlignment(.center)
                        
                        Text("Add a card to send and receive money through Dash")
                            .font(Font.theme.bodyMedium)
                            .foregroundColor(Color.theme.textSecondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, Spacing.lg)
                    }
                    
                    Spacer()
                    
                    // Content
                    if isSetupComplete {
                        setupCompleteContent
                    } else {
                        setupContent
                    }
                    
                    Spacer()
                    
                    // Action buttons
                    VStack(spacing: Spacing.md) {
                        if let errorMessage = stripeConnectService.errorMessage {
                            ModernCard(padding: Spacing.md, cornerRadius: BorderRadius.md) {
                                HStack(spacing: Spacing.sm) {
                                    Image(systemName: "exclamationmark.triangle.fill")
                                        .foregroundColor(Color.theme.error)
                                        .font(.system(size: 16, weight: .medium))
                                    
                                    Text(errorMessage)
                                        .font(Font.theme.bodyMedium)
                                        .foregroundColor(Color.theme.error)
                                        .multilineTextAlignment(.leading)
                                    
                                    Spacer()
                                }
                            }
                        }
                        
                        if let successMessage = stripeConnectService.successMessage {
                            ModernCard(padding: Spacing.md, cornerRadius: BorderRadius.md) {
                                HStack(spacing: Spacing.sm) {
                                    Image(systemName: "checkmark.circle.fill")
                                        .foregroundColor(Color.theme.success)
                                        .font(.system(size: 16, weight: .medium))
                                    
                                    Text(successMessage)
                                        .font(Font.theme.bodyMedium)
                                        .foregroundColor(Color.theme.success)
                                        .multilineTextAlignment(.leading)
                                    
                                    Spacer()
                                }
                            }
                        }
                        
                        actionButton
                    }
                    .padding(.horizontal, Spacing.lg)
                }
                .padding(.vertical, Spacing.xl)
            }
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("Cancel") {
                    presentationMode.wrappedValue.dismiss()
                }
                .foregroundColor(Color.theme.textSecondary)
            )
        }
        .paymentSheet(isPresented: Binding<Bool>(
            get: { paymentSheet != nil },
            set: { _ in }
        ), paymentSheet: paymentSheet) { result in
            paymentResult = result
            handlePaymentSheetResult(result)
        }
        .onAppear {
            checkCurrentStatus()
        }
    }
    
    // MARK: - Content Views
    
    private var setupContent: some View {
        VStack(spacing: Spacing.lg) {
            ModernCard(padding: Spacing.lg, cornerRadius: BorderRadius.lg) {
                VStack(alignment: .leading, spacing: Spacing.md) {
                    Text("Why do I need to add a card?")
                        .font(Font.theme.bodyLarge)
                        .fontWeight(.semibold)
                        .foregroundColor(Color.theme.textPrimary)
                    
                    VStack(alignment: .leading, spacing: Spacing.sm) {
                        FeatureRow(icon: "arrow.up.circle.fill", text: "Send money to friends", color: Color.theme.accent)
                        FeatureRow(icon: "arrow.down.circle.fill", text: "Receive money from splits", color: Color.theme.success)
                        FeatureRow(icon: "person.3.fill", text: "Join group expenses", color: Color.theme.warning)
                        FeatureRow(icon: "shield.fill", text: "Secure payments with Stripe", color: Color.theme.info)
                    }
                }
            }
            .padding(.horizontal, Spacing.lg)
            
            ModernCard(padding: Spacing.lg, cornerRadius: BorderRadius.lg) {
                VStack(spacing: Spacing.md) {
                    HStack(spacing: Spacing.sm) {
                        Image(systemName: "lock.shield.fill")
                            .font(.system(size: 20))
                            .foregroundColor(Color.theme.success)
                        
                        Text("Your information is secure")
                            .font(Font.theme.bodyMedium)
                            .fontWeight(.semibold)
                            .foregroundColor(Color.theme.textPrimary)
                        
                        Spacer()
                    }
                    
                    Text("We use Stripe, a trusted payment processor used by millions of businesses worldwide. Your card information is encrypted and never stored on our servers.")
                        .font(Font.theme.bodySmall)
                        .foregroundColor(Color.theme.textSecondary)
                        .multilineTextAlignment(.leading)
                }
            }
            .padding(.horizontal, Spacing.lg)
        }
    }
    
    private var setupCompleteContent: some View {
        VStack(spacing: Spacing.lg) {
            ModernCard(padding: Spacing.lg, cornerRadius: BorderRadius.lg) {
                VStack(spacing: Spacing.md) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 40))
                        .foregroundColor(Color.theme.success)
                    
                    Text("Payment Method Added!")
                        .font(Font.theme.bodyLarge)
                        .fontWeight(.semibold)
                        .foregroundColor(Color.theme.textPrimary)
                    
                    Text("You can now send money, join splits, and use all Dash features.")
                        .font(Font.theme.bodyMedium)
                        .foregroundColor(Color.theme.textSecondary)
                        .multilineTextAlignment(.center)
                }
            }
            .padding(.horizontal, Spacing.lg)
        }
    }
    
    // MARK: - Action Button
    
    private var actionButton: some View {
        ModernButton(
            isSetupComplete ? "Done" : "Add Payment Method",
            icon: isSetupComplete ? "checkmark.circle.fill" : "creditcard.fill",
            style: .primary,
            size: .large,
            isLoading: stripeConnectService.isLoading
        ) {
            if isSetupComplete {
                presentationMode.wrappedValue.dismiss()
            } else {
                setupPaymentMethod()
            }
        }
    }
    
    // MARK: - Actions
    
    private func checkCurrentStatus() {
        guard let user = authViewModel.currentUser else { return }
        isSetupComplete = !user.needsPaymentMethodSetup
    }
    
    private func setupPaymentMethod() {
        guard let user = authViewModel.currentUser,
              let userId = user.uid else { return }
        
        Task {
            do {
                let clientSecret = try await stripeConnectService.setupPaymentMethod(for: userId)
                
                await MainActor.run {
                    var configuration = PaymentSheet.Configuration()
                    configuration.merchantDisplayName = "Dash Finance"
                    configuration.allowsDelayedPaymentMethods = false
                    configuration.returnURL = "dash://payment-setup-complete"
                    
                    self.paymentSheet = PaymentSheet(
                        setupIntentClientSecret: clientSecret,
                        configuration: configuration
                    )
                }
            } catch {
                // Error is handled by the service
            }
        }
    }
    
    private func handlePaymentSheetResult(_ result: PaymentSheetResult) {
        switch result {
        case .completed:
            isSetupComplete = true
            
            // Update user's payment method status
            if let user = authViewModel.currentUser,
               let userId = user.uid {
                Task {
                    do {
                        try await stripeConnectService.confirmPaymentMethodSetup(
                            userId: userId,
                            paymentMethodId: "pm_placeholder" // This would come from the payment sheet
                        )
                        
                        // Refresh user data
                        await authViewModel.refreshCurrentUser()
                    } catch {
                        // Error is handled by the service
                    }
                }
            }
            
        case .canceled:
            print("Payment setup was canceled")
            
        case .failed(let error):
            print("Payment setup failed: \(error)")
            stripeConnectService.errorMessage = "Payment setup failed. Please try again."
        }
        
        paymentSheet = nil
    }
}

// MARK: - Supporting Views

struct FeatureRow: View {
    let icon: String
    let text: String
    let color: Color
    
    var body: some View {
        HStack(spacing: Spacing.sm) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(color)
                .frame(width: 20)
            
            Text(text)
                .font(Font.theme.bodyMedium)
                .foregroundColor(Color.theme.textPrimary)
            
            Spacer()
        }
    }
}

#Preview {
    PaymentMethodSetupView()
        .environmentObject(AuthViewModel.shared)
}
