import SwiftUI
import SafariServices

struct StripeConnectOnboardingView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var stripeConnectService = StripeConnectService.shared
    
    @State private var showingSafariView = false
    @State private var onboardingURL: String?
    @State private var currentStep: OnboardingStep = .welcome
    
    enum OnboardingStep {
        case welcome
        case creatingAccount
        case onboarding
        case paymentMethod
        case identityVerification
        case complete
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.theme.background
                    .ignoresSafeArea()
                
                VStack(spacing: Spacing.xl) {
                    // Header
                    VStack(spacing: Spacing.md) {
                        Image(systemName: stepIcon)
                            .font(.system(size: 60, weight: .light))
                            .foregroundColor(Color.theme.accent)
                        
                        Text(stepTitle)
                            .font(Font.theme.headlineLarge)
                            .fontWeight(.bold)
                            .foregroundColor(Color.theme.textPrimary)
                            .multilineTextAlignment(.center)
                        
                        Text(stepDescription)
                            .font(Font.theme.bodyMedium)
                            .foregroundColor(Color.theme.textSecondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, Spacing.lg)
                    }
                    
                    Spacer()
                    
                    // Content based on current step
                    switch currentStep {
                    case .welcome:
                        welcomeContent
                    case .creatingAccount:
                        loadingContent
                    case .onboarding:
                        onboardingContent
                    case .paymentMethod:
                        paymentMethodContent
                    case .identityVerification:
                        identityVerificationContent
                    case .complete:
                        completeContent
                    }
                    
                    Spacer()
                    
                    // Action buttons
                    VStack(spacing: Spacing.md) {
                        if let errorMessage = stripeConnectService.errorMessage {
                            ModernCard(padding: Spacing.md, cornerRadius: BorderRadius.md) {
                                HStack(spacing: Spacing.sm) {
                                    Image(systemName: "exclamationmark.triangle.fill")
                                        .foregroundColor(Color.theme.error)
                                        .font(.system(size: 16, weight: .medium))
                                    
                                    Text(errorMessage)
                                        .font(Font.theme.bodyMedium)
                                        .foregroundColor(Color.theme.error)
                                        .multilineTextAlignment(.leading)
                                    
                                    Spacer()
                                }
                            }
                        }
                        
                        actionButton
                    }
                    .padding(.horizontal, Spacing.lg)
                }
                .padding(.vertical, Spacing.xl)
            }
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("Cancel") {
                    presentationMode.wrappedValue.dismiss()
                }
                .foregroundColor(Color.theme.textSecondary)
            )
        }
        .sheet(isPresented: $showingSafariView) {
            if let urlString = onboardingURL, let url = URL(string: urlString) {
                SafariView(url: url) { result in
                    handleOnboardingResult(result)
                }
            }
        }
        .onAppear {
            checkCurrentUserStatus()
        }
    }
    
    // MARK: - Step Content Views
    
    private var welcomeContent: some View {
        VStack(spacing: Spacing.lg) {
            ModernCard(padding: Spacing.lg, cornerRadius: BorderRadius.lg) {
                VStack(alignment: .leading, spacing: Spacing.md) {
                    Text("What you'll need:")
                        .font(Font.theme.bodyLarge)
                        .fontWeight(.semibold)
                        .foregroundColor(Color.theme.textPrimary)
                    
                    VStack(alignment: .leading, spacing: Spacing.sm) {
                        RequirementRow(icon: "person.fill", text: "Personal information")
                        RequirementRow(icon: "creditcard.fill", text: "Bank account or debit card")
                        RequirementRow(icon: "doc.text.fill", text: "Government-issued ID")
                        RequirementRow(icon: "checkmark.shield.fill", text: "Identity verification")
                    }
                }
            }
            .padding(.horizontal, Spacing.lg)
        }
    }
    
    private var loadingContent: some View {
        VStack(spacing: Spacing.lg) {
            ProgressView()
                .scaleEffect(1.5)
                .progressViewStyle(CircularProgressViewStyle(tint: Color.theme.accent))
            
            Text("Setting up your account...")
                .font(Font.theme.bodyMedium)
                .foregroundColor(Color.theme.textSecondary)
        }
    }
    
    private var onboardingContent: some View {
        VStack(spacing: Spacing.lg) {
            ModernCard(padding: Spacing.lg, cornerRadius: BorderRadius.lg) {
                VStack(spacing: Spacing.md) {
                    Image(systemName: "safari.fill")
                        .font(.system(size: 40))
                        .foregroundColor(Color.theme.accent)
                    
                    Text("Complete Setup in Browser")
                        .font(Font.theme.bodyLarge)
                        .fontWeight(.semibold)
                        .foregroundColor(Color.theme.textPrimary)
                    
                    Text("You'll be redirected to Stripe's secure platform to complete your account setup.")
                        .font(Font.theme.bodyMedium)
                        .foregroundColor(Color.theme.textSecondary)
                        .multilineTextAlignment(.center)
                }
            }
            .padding(.horizontal, Spacing.lg)
        }
    }
    
    private var paymentMethodContent: some View {
        VStack(spacing: Spacing.lg) {
            ModernCard(padding: Spacing.lg, cornerRadius: BorderRadius.lg) {
                VStack(spacing: Spacing.md) {
                    Image(systemName: "creditcard.circle.fill")
                        .font(.system(size: 40))
                        .foregroundColor(Color.theme.accent)
                    
                    Text("Add Payment Method")
                        .font(Font.theme.bodyLarge)
                        .fontWeight(.semibold)
                        .foregroundColor(Color.theme.textPrimary)
                    
                    Text("Add a card to send and receive money through Dash.")
                        .font(Font.theme.bodyMedium)
                        .foregroundColor(Color.theme.textSecondary)
                        .multilineTextAlignment(.center)
                }
            }
            .padding(.horizontal, Spacing.lg)
        }
    }
    
    private var identityVerificationContent: some View {
        VStack(spacing: Spacing.lg) {
            ModernCard(padding: Spacing.lg, cornerRadius: BorderRadius.lg) {
                VStack(spacing: Spacing.md) {
                    Image(systemName: "checkmark.shield.fill")
                        .font(.system(size: 40))
                        .foregroundColor(Color.theme.accent)
                    
                    Text("Verify Your Identity")
                        .font(Font.theme.bodyLarge)
                        .fontWeight(.semibold)
                        .foregroundColor(Color.theme.textPrimary)
                    
                    Text("Complete identity verification to unlock all Dash features.")
                        .font(Font.theme.bodyMedium)
                        .foregroundColor(Color.theme.textSecondary)
                        .multilineTextAlignment(.center)
                }
            }
            .padding(.horizontal, Spacing.lg)
        }
    }
    
    private var completeContent: some View {
        VStack(spacing: Spacing.lg) {
            ModernCard(padding: Spacing.lg, cornerRadius: BorderRadius.lg) {
                VStack(spacing: Spacing.md) {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 40))
                        .foregroundColor(Color.theme.success)
                    
                    Text("Setup Complete!")
                        .font(Font.theme.bodyLarge)
                        .fontWeight(.semibold)
                        .foregroundColor(Color.theme.textPrimary)
                    
                    Text("You can now send money, join splits, and use all Dash features.")
                        .font(Font.theme.bodyMedium)
                        .foregroundColor(Color.theme.textSecondary)
                        .multilineTextAlignment(.center)
                }
            }
            .padding(.horizontal, Spacing.lg)
        }
    }
    
    // MARK: - Action Button
    
    private var actionButton: some View {
        ModernButton(
            buttonText,
            icon: buttonIcon,
            style: .primary,
            size: .large,
            isLoading: stripeConnectService.isLoading,
            isDisabled: buttonDisabled
        ) {
            handleButtonAction()
        }
    }
    
    // MARK: - Computed Properties
    
    private var stepIcon: String {
        switch currentStep {
        case .welcome: return "hand.wave.fill"
        case .creatingAccount: return "gear.circle.fill"
        case .onboarding: return "safari.fill"
        case .paymentMethod: return "creditcard.circle.fill"
        case .identityVerification: return "checkmark.shield.fill"
        case .complete: return "checkmark.circle.fill"
        }
    }
    
    private var stepTitle: String {
        switch currentStep {
        case .welcome: return "Welcome to Dash"
        case .creatingAccount: return "Creating Account"
        case .onboarding: return "Complete Setup"
        case .paymentMethod: return "Add Payment Method"
        case .identityVerification: return "Verify Identity"
        case .complete: return "All Set!"
        }
    }
    
    private var stepDescription: String {
        switch currentStep {
        case .welcome: return "Let's set up your account so you can start splitting bills and sending money."
        case .creatingAccount: return "We're setting up your secure payment account."
        case .onboarding: return "Complete your account setup with our secure partner, Stripe."
        case .paymentMethod: return "Add a payment method to send and receive money."
        case .identityVerification: return "Verify your identity to unlock all features."
        case .complete: return "Your account is ready to use!"
        }
    }
    
    private var buttonText: String {
        switch currentStep {
        case .welcome: return "Get Started"
        case .creatingAccount: return "Creating..."
        case .onboarding: return "Continue Setup"
        case .paymentMethod: return "Add Payment Method"
        case .identityVerification: return "Verify Identity"
        case .complete: return "Done"
        }
    }
    
    private var buttonIcon: String {
        switch currentStep {
        case .welcome: return "arrow.right.circle.fill"
        case .creatingAccount: return "gear.circle.fill"
        case .onboarding: return "safari.fill"
        case .paymentMethod: return "creditcard.fill"
        case .identityVerification: return "checkmark.shield.fill"
        case .complete: return "checkmark.circle.fill"
        }
    }
    
    private var buttonDisabled: Bool {
        return currentStep == .creatingAccount
    }
    
    // MARK: - Actions
    
    private func handleButtonAction() {
        switch currentStep {
        case .welcome:
            startAccountCreation()
        case .onboarding:
            startOnboarding()
        case .paymentMethod:
            setupPaymentMethod()
        case .identityVerification:
            startIdentityVerification()
        case .complete:
            presentationMode.wrappedValue.dismiss()
        default:
            break
        }
    }
    
    private func checkCurrentUserStatus() {
        guard let user = authViewModel.currentUser else { return }
        
        if user.needsStripeOnboarding {
            currentStep = .welcome
        } else if user.needsPaymentMethodSetup {
            currentStep = .paymentMethod
        } else if user.needsIdentityVerification {
            currentStep = .identityVerification
        } else {
            currentStep = .complete
        }
    }
    
    private func startAccountCreation() {
        guard let user = authViewModel.currentUser else { return }
        
        currentStep = .creatingAccount
        
        Task {
            do {
                let accountId = try await stripeConnectService.createConnectedAccount(for: user)
                await MainActor.run {
                    self.currentStep = .onboarding
                }
            } catch {
                await MainActor.run {
                    self.currentStep = .welcome
                }
            }
        }
    }
    
    private func startOnboarding() {
        guard let user = authViewModel.currentUser,
              let accountId = user.stripeAccountId,
              let userId = user.uid else { return }
        
        Task {
            do {
                let url = try await stripeConnectService.createAccountLink(accountId: accountId, userId: userId)
                await MainActor.run {
                    self.onboardingURL = url
                    self.showingSafariView = true
                }
            } catch {
                // Error is handled by the service
            }
        }
    }
    
    private func setupPaymentMethod() {
        // Navigate to payment method setup
        // This would typically present a Stripe payment sheet
        currentStep = .identityVerification
    }
    
    private func startIdentityVerification() {
        // Navigate to identity verification
        // This would typically use Stripe Identity
        currentStep = .complete
    }
    
    private func handleOnboardingResult(_ result: SafariView.Result) {
        showingSafariView = false
        
        // Check account status after onboarding
        guard let user = authViewModel.currentUser,
              let accountId = user.stripeAccountId else { return }
        
        Task {
            do {
                let status = try await stripeConnectService.checkAccountStatus(accountId: accountId)
                await MainActor.run {
                    if status == .active {
                        self.currentStep = .paymentMethod
                    } else {
                        self.currentStep = .onboarding
                    }
                }
            } catch {
                // Error is handled by the service
            }
        }
    }
}

// MARK: - Supporting Views

struct RequirementRow: View {
    let icon: String
    let text: String
    
    var body: some View {
        HStack(spacing: Spacing.sm) {
            Image(systemName: icon)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(Color.theme.accent)
                .frame(width: 20)
            
            Text(text)
                .font(Font.theme.bodyMedium)
                .foregroundColor(Color.theme.textPrimary)
            
            Spacer()
        }
    }
}

struct SafariView: UIViewControllerRepresentable {
    let url: URL
    let onDismiss: (Result) -> Void
    
    enum Result {
        case completed
        case cancelled
    }
    
    func makeUIViewController(context: Context) -> SFSafariViewController {
        let safari = SFSafariViewController(url: url)
        safari.delegate = context.coordinator
        return safari
    }
    
    func updateUIViewController(_ uiViewController: SFSafariViewController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, SFSafariViewControllerDelegate {
        let parent: SafariView
        
        init(_ parent: SafariView) {
            self.parent = parent
        }
        
        func safariViewControllerDidFinish(_ controller: SFSafariViewController) {
            parent.onDismiss(.completed)
        }
    }
}

#Preview {
    StripeConnectOnboardingView()
        .environmentObject(AuthViewModel.shared)
}
