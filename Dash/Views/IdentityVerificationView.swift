import SwiftUI
import SafariServices

struct IdentityVerificationView: View {
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var authViewModel: AuthViewModel
    @StateObject private var stripeConnectService = StripeConnectService.shared
    
    @State private var showingSafariView = false
    @State private var verificationURL: String?
    @State private var verificationStatus: VerificationStatus = .notStarted
    @State private var isCheckingStatus = false
    
    enum VerificationStatus {
        case notStarted
        case inProgress
        case verified
        case failed
        case requiresInput
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.theme.background
                    .ignoresSafeArea()
                
                VStack(spacing: Spacing.xl) {
                    // Header
                    VStack(spacing: Spacing.md) {
                        Image(systemName: statusIcon)
                            .font(.system(size: 60, weight: .light))
                            .foregroundColor(statusColor)
                        
                        Text(statusTitle)
                            .font(Font.theme.headlineLarge)
                            .fontWeight(.bold)
                            .foregroundColor(Color.theme.textPrimary)
                            .multilineTextAlignment(.center)
                        
                        Text(statusDescription)
                            .font(Font.theme.bodyMedium)
                            .foregroundColor(Color.theme.textSecondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, Spacing.lg)
                    }
                    
                    Spacer()
                    
                    // Content based on verification status
                    switch verificationStatus {
                    case .notStarted:
                        notStartedContent
                    case .inProgress:
                        inProgressContent
                    case .verified:
                        verifiedContent
                    case .failed:
                        failedContent
                    case .requiresInput:
                        requiresInputContent
                    }
                    
                    Spacer()
                    
                    // Action buttons
                    VStack(spacing: Spacing.md) {
                        if let errorMessage = stripeConnectService.errorMessage {
                            ModernCard(padding: Spacing.md, cornerRadius: BorderRadius.md) {
                                HStack(spacing: Spacing.sm) {
                                    Image(systemName: "exclamationmark.triangle.fill")
                                        .foregroundColor(Color.theme.error)
                                        .font(.system(size: 16, weight: .medium))
                                    
                                    Text(errorMessage)
                                        .font(Font.theme.bodyMedium)
                                        .foregroundColor(Color.theme.error)
                                        .multilineTextAlignment(.leading)
                                    
                                    Spacer()
                                }
                            }
                        }
                        
                        actionButton
                    }
                    .padding(.horizontal, Spacing.lg)
                }
                .padding(.vertical, Spacing.xl)
            }
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("Cancel") {
                    presentationMode.wrappedValue.dismiss()
                }
                .foregroundColor(Color.theme.textSecondary)
            )
        }
        .sheet(isPresented: $showingSafariView) {
            if let urlString = verificationURL, let url = URL(string: urlString) {
                SafariView(url: url) { result in
                    handleVerificationResult(result)
                }
            }
        }
        .onAppear {
            checkCurrentVerificationStatus()
        }
    }
    
    // MARK: - Content Views
    
    private var notStartedContent: some View {
        VStack(spacing: Spacing.lg) {
            ModernCard(padding: Spacing.lg, cornerRadius: BorderRadius.lg) {
                VStack(alignment: .leading, spacing: Spacing.md) {
                    Text("What you'll need:")
                        .font(Font.theme.bodyLarge)
                        .fontWeight(.semibold)
                        .foregroundColor(Color.theme.textPrimary)
                    
                    VStack(alignment: .leading, spacing: Spacing.sm) {
                        RequirementRow(icon: "doc.text.fill", text: "Government-issued photo ID")
                        RequirementRow(icon: "camera.fill", text: "Device camera for selfie")
                        RequirementRow(icon: "clock.fill", text: "About 2-3 minutes")
                        RequirementRow(icon: "shield.fill", text: "Secure verification process")
                    }
                }
            }
            .padding(.horizontal, Spacing.lg)
            
            ModernCard(padding: Spacing.lg, cornerRadius: BorderRadius.lg) {
                VStack(spacing: Spacing.md) {
                    HStack(spacing: Spacing.sm) {
                        Image(systemName: "lock.shield.fill")
                            .font(.system(size: 20))
                            .foregroundColor(Color.theme.success)
                        
                        Text("Your privacy is protected")
                            .font(Font.theme.bodyMedium)
                            .fontWeight(.semibold)
                            .foregroundColor(Color.theme.textPrimary)
                        
                        Spacer()
                    }
                    
                    Text("Identity verification is handled by Stripe, a trusted financial services provider. Your information is encrypted and securely processed.")
                        .font(Font.theme.bodySmall)
                        .foregroundColor(Color.theme.textSecondary)
                        .multilineTextAlignment(.leading)
                }
            }
            .padding(.horizontal, Spacing.lg)
        }
    }
    
    private var inProgressContent: some View {
        VStack(spacing: Spacing.lg) {
            if isCheckingStatus {
                ProgressView()
                    .scaleEffect(1.5)
                    .progressViewStyle(CircularProgressViewStyle(tint: Color.theme.accent))
                
                Text("Checking verification status...")
                    .font(Font.theme.bodyMedium)
                    .foregroundColor(Color.theme.textSecondary)
            } else {
                ModernCard(padding: Spacing.lg, cornerRadius: BorderRadius.lg) {
                    VStack(spacing: Spacing.md) {
                        Image(systemName: "hourglass.circle.fill")
                            .font(.system(size: 40))
                            .foregroundColor(Color.theme.warning)
                        
                        Text("Verification in Progress")
                            .font(Font.theme.bodyLarge)
                            .fontWeight(.semibold)
                            .foregroundColor(Color.theme.textPrimary)
                        
                        Text("Your identity verification is being processed. This usually takes a few minutes.")
                            .font(Font.theme.bodyMedium)
                            .foregroundColor(Color.theme.textSecondary)
                            .multilineTextAlignment(.center)
                    }
                }
                .padding(.horizontal, Spacing.lg)
            }
        }
    }
    
    private var verifiedContent: some View {
        VStack(spacing: Spacing.lg) {
            ModernCard(padding: Spacing.lg, cornerRadius: BorderRadius.lg) {
                VStack(spacing: Spacing.md) {
                    Image(systemName: "checkmark.seal.fill")
                        .font(.system(size: 40))
                        .foregroundColor(Color.theme.success)
                    
                    Text("Identity Verified!")
                        .font(Font.theme.bodyLarge)
                        .fontWeight(.semibold)
                        .foregroundColor(Color.theme.textPrimary)
                    
                    Text("Your identity has been successfully verified. You can now use all Dash features.")
                        .font(Font.theme.bodyMedium)
                        .foregroundColor(Color.theme.textSecondary)
                        .multilineTextAlignment(.center)
                }
            }
            .padding(.horizontal, Spacing.lg)
        }
    }
    
    private var failedContent: some View {
        VStack(spacing: Spacing.lg) {
            ModernCard(padding: Spacing.lg, cornerRadius: BorderRadius.lg) {
                VStack(spacing: Spacing.md) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 40))
                        .foregroundColor(Color.theme.error)
                    
                    Text("Verification Failed")
                        .font(Font.theme.bodyLarge)
                        .fontWeight(.semibold)
                        .foregroundColor(Color.theme.textPrimary)
                    
                    Text("We couldn't verify your identity. Please try again or contact support if you continue to have issues.")
                        .font(Font.theme.bodyMedium)
                        .foregroundColor(Color.theme.textSecondary)
                        .multilineTextAlignment(.center)
                }
            }
            .padding(.horizontal, Spacing.lg)
        }
    }
    
    private var requiresInputContent: some View {
        VStack(spacing: Spacing.lg) {
            ModernCard(padding: Spacing.lg, cornerRadius: BorderRadius.lg) {
                VStack(spacing: Spacing.md) {
                    Image(systemName: "doc.text.magnifyingglass")
                        .font(.system(size: 40))
                        .foregroundColor(Color.theme.warning)
                    
                    Text("Additional Information Needed")
                        .font(Font.theme.bodyLarge)
                        .fontWeight(.semibold)
                        .foregroundColor(Color.theme.textPrimary)
                    
                    Text("We need additional information to complete your verification. Please continue the process.")
                        .font(Font.theme.bodyMedium)
                        .foregroundColor(Color.theme.textSecondary)
                        .multilineTextAlignment(.center)
                }
            }
            .padding(.horizontal, Spacing.lg)
        }
    }
    
    // MARK: - Action Button
    
    private var actionButton: some View {
        ModernButton(
            buttonText,
            icon: buttonIcon,
            style: buttonStyle,
            size: .large,
            isLoading: stripeConnectService.isLoading || isCheckingStatus,
            isDisabled: buttonDisabled
        ) {
            handleButtonAction()
        }
    }
    
    // MARK: - Computed Properties
    
    private var statusIcon: String {
        switch verificationStatus {
        case .notStarted: return "person.badge.plus.fill"
        case .inProgress: return "hourglass.circle.fill"
        case .verified: return "checkmark.seal.fill"
        case .failed: return "xmark.circle.fill"
        case .requiresInput: return "doc.text.magnifyingglass"
        }
    }
    
    private var statusColor: Color {
        switch verificationStatus {
        case .notStarted: return Color.theme.accent
        case .inProgress: return Color.theme.warning
        case .verified: return Color.theme.success
        case .failed: return Color.theme.error
        case .requiresInput: return Color.theme.warning
        }
    }
    
    private var statusTitle: String {
        switch verificationStatus {
        case .notStarted: return "Verify Your Identity"
        case .inProgress: return "Verification in Progress"
        case .verified: return "Identity Verified"
        case .failed: return "Verification Failed"
        case .requiresInput: return "Additional Info Needed"
        }
    }
    
    private var statusDescription: String {
        switch verificationStatus {
        case .notStarted: return "Complete identity verification to unlock all Dash features and ensure secure transactions."
        case .inProgress: return "Your verification is being processed. This usually takes just a few minutes."
        case .verified: return "Your identity has been successfully verified. You're all set!"
        case .failed: return "We couldn't verify your identity. Please try again."
        case .requiresInput: return "We need additional information to complete your verification."
        }
    }
    
    private var buttonText: String {
        switch verificationStatus {
        case .notStarted: return "Start Verification"
        case .inProgress: return "Check Status"
        case .verified: return "Done"
        case .failed: return "Try Again"
        case .requiresInput: return "Continue Verification"
        }
    }
    
    private var buttonIcon: String {
        switch verificationStatus {
        case .notStarted: return "person.badge.plus.fill"
        case .inProgress: return "arrow.clockwise.circle.fill"
        case .verified: return "checkmark.circle.fill"
        case .failed: return "arrow.clockwise.circle.fill"
        case .requiresInput: return "arrow.right.circle.fill"
        }
    }
    
    private var buttonStyle: ModernButton.Style {
        switch verificationStatus {
        case .failed: return .secondary
        default: return .primary
        }
    }
    
    private var buttonDisabled: Bool {
        return false
    }
    
    // MARK: - Actions
    
    private func handleButtonAction() {
        switch verificationStatus {
        case .notStarted, .requiresInput:
            startIdentityVerification()
        case .inProgress:
            checkVerificationStatus()
        case .verified:
            presentationMode.wrappedValue.dismiss()
        case .failed:
            startIdentityVerification()
        }
    }
    
    private func checkCurrentVerificationStatus() {
        guard let user = authViewModel.currentUser else { return }
        
        if user.identityVerified {
            verificationStatus = .verified
        } else {
            verificationStatus = .notStarted
        }
    }
    
    private func startIdentityVerification() {
        // In a real implementation, this would create a Stripe Identity verification session
        // For now, we'll simulate the process
        verificationURL = "https://verify.stripe.com/start/test_verification_session"
        showingSafariView = true
    }
    
    private func checkVerificationStatus() {
        isCheckingStatus = true
        
        // Simulate checking verification status
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            self.isCheckingStatus = false
            // In a real implementation, this would check with Stripe Identity API
            self.verificationStatus = .verified
            
            // Update user's verification status
            if let user = self.authViewModel.currentUser,
               let userId = user.uid {
                Task {
                    try? await self.updateUserVerificationStatus(userId: userId, verified: true)
                    await self.authViewModel.refreshCurrentUser()
                }
            }
        }
    }
    
    private func handleVerificationResult(_ result: SafariView.Result) {
        showingSafariView = false
        verificationStatus = .inProgress
        
        // Check status after a delay to allow processing
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            self.checkVerificationStatus()
        }
    }
    
    private func updateUserVerificationStatus(userId: String, verified: Bool) async throws {
        let userRef = Firestore.firestore().collection("users").document(userId)
        
        try await userRef.updateData([
            "identityVerified": verified,
            "identityVerificationDate": Timestamp(date: Date())
        ])
    }
}

#Preview {
    IdentityVerificationView()
        .environmentObject(AuthViewModel.shared)
}
