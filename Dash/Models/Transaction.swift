import Foundation
import FirebaseFirestore

struct Transaction: Identifiable, Codable {
    @DocumentID var id: String?
    let name: String
    let detail: String
    let amount: Double
    let type: TransactionType
    let category: String
    let date: Timestamp
    var status: TransactionStatus
    var splitId: String?
    
    // For money transfers
    var recipientId: String?
    var recipientName: String?
    var senderId: String?
    var senderName: String?
    
    // Additional metadata
    var transactionHash: String?
    var notes: String?
    var attachments: [String]? // URLs to attached files/images

    // Stripe Connect fields
    var stripePaymentIntentId: String?
    var stripeTransferId: String?
    var stripeChargeId: String?
    
    init(name: String, detail: String, amount: Double, type: TransactionType, category: String, date: Timestamp = Timestamp(date: Date()), status: TransactionStatus = .completed, splitId: String? = nil, recipientId: String? = nil, recipientName: String? = nil, senderId: String? = nil, senderName: String? = nil, transactionHash: String? = nil, notes: String? = nil, attachments: [String]? = nil, stripePaymentIntentId: String? = nil, stripeTransferId: String? = nil, stripeChargeId: String? = nil) {
        // Don't set id - let Firestore manage it with @DocumentID
        self.name = name
        self.detail = detail
        self.amount = amount
        self.type = type
        self.category = category
        self.date = date
        self.status = status
        self.splitId = splitId
        self.recipientId = recipientId
        self.recipientName = recipientName
        self.senderId = senderId
        self.senderName = senderName
        self.transactionHash = transactionHash
        self.notes = notes
        self.attachments = attachments
        self.stripePaymentIntentId = stripePaymentIntentId
        self.stripeTransferId = stripeTransferId
        self.stripeChargeId = stripeChargeId
    }
    
    // Custom decoder to handle missing status field
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        // Decode required fields
        name = try container.decode(String.self, forKey: .name)
        detail = try container.decode(String.self, forKey: .detail)
        amount = try container.decode(Double.self, forKey: .amount)
        type = try container.decode(TransactionType.self, forKey: .type)
        category = try container.decode(String.self, forKey: .category)
        date = try container.decode(Timestamp.self, forKey: .date)
        
        // Decode status with default value if missing
        status = try container.decodeIfPresent(TransactionStatus.self, forKey: .status) ?? .completed
        
        // Decode optional fields
        splitId = try container.decodeIfPresent(String.self, forKey: .splitId)
        recipientId = try container.decodeIfPresent(String.self, forKey: .recipientId)
        recipientName = try container.decodeIfPresent(String.self, forKey: .recipientName)
        senderId = try container.decodeIfPresent(String.self, forKey: .senderId)
        senderName = try container.decodeIfPresent(String.self, forKey: .senderName)
        transactionHash = try container.decodeIfPresent(String.self, forKey: .transactionHash)
        notes = try container.decodeIfPresent(String.self, forKey: .notes)
        attachments = try container.decodeIfPresent([String].self, forKey: .attachments)
        stripePaymentIntentId = try container.decodeIfPresent(String.self, forKey: .stripePaymentIntentId)
        stripeTransferId = try container.decodeIfPresent(String.self, forKey: .stripeTransferId)
        stripeChargeId = try container.decodeIfPresent(String.self, forKey: .stripeChargeId)
    }
    
    // Custom encoder to ensure all fields are encoded
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        try container.encode(name, forKey: .name)
        try container.encode(detail, forKey: .detail)
        try container.encode(amount, forKey: .amount)
        try container.encode(type, forKey: .type)
        try container.encode(category, forKey: .category)
        try container.encode(date, forKey: .date)
        try container.encode(status, forKey: .status)
        
        try container.encodeIfPresent(splitId, forKey: .splitId)
        try container.encodeIfPresent(recipientId, forKey: .recipientId)
        try container.encodeIfPresent(recipientName, forKey: .recipientName)
        try container.encodeIfPresent(senderId, forKey: .senderId)
        try container.encodeIfPresent(senderName, forKey: .senderName)
        try container.encodeIfPresent(transactionHash, forKey: .transactionHash)
        try container.encodeIfPresent(notes, forKey: .notes)
        try container.encodeIfPresent(attachments, forKey: .attachments)
        try container.encodeIfPresent(stripePaymentIntentId, forKey: .stripePaymentIntentId)
        try container.encodeIfPresent(stripeTransferId, forKey: .stripeTransferId)
        try container.encodeIfPresent(stripeChargeId, forKey: .stripeChargeId)
    }
    
    // Coding keys for custom encoding/decoding
    private enum CodingKeys: String, CodingKey {
        case name, detail, amount, type, category, date, status, splitId
        case recipientId, recipientName, senderId, senderName
        case transactionHash, notes, attachments
        case stripePaymentIntentId, stripeTransferId, stripeChargeId
    }
}

enum TransactionType: String, Codable, CaseIterable {
    case income = "income"
    case expense = "expense"
    case transfer = "transfer"
    case addMoney = "add_money"
    case settlement = "settlement"
    case splitPayment = "split_payment"
    case stripeTransfer = "stripe_transfer"

    var displayName: String {
        switch self {
        case .income: return "Income"
        case .expense: return "Expense"
        case .transfer: return "Transfer"
        case .addMoney: return "Add Money"
        case .settlement: return "Settlement"
        case .splitPayment: return "Split Payment"
        case .stripeTransfer: return "Stripe Transfer"
        }
    }
}

enum TransactionStatus: String, Codable, CaseIterable {
    case pending = "pending"
    case completed = "completed"
    case cancelled = "cancelled"
    case failed = "failed"
    
    var displayName: String {
        switch self {
        case .pending: return "Pending"
        case .completed: return "Completed"
        case .cancelled: return "Cancelled"
        case .failed: return "Failed"
        }
    }
}
