import Foundation
import FirebaseFirestore

enum SecurityLevel: String, Codable, CaseIterable {
    case basic = "basic"
    case enhanced = "enhanced"
    case maximum = "maximum"

    var displayName: String {
        switch self {
        case .basic: return "Basic"
        case .enhanced: return "Enhanced"
        case .maximum: return "Maximum"
        }
    }
}



struct TrustedDevice: Codable, Equatable {
    let deviceId: String
    let deviceName: String
    let addedDate: Date
    let lastUsed: Date
    
    init(deviceId: String, deviceName: String) {
        self.deviceId = deviceId
        self.deviceName = deviceName
        self.addedDate = Date()
        self.lastUsed = Date()
    }
}

struct User: Identifiable, Codable, Equatable {
    var id: String?
    var uid: String?
    var email: String?
    var displayName: String?
    var balance: Double?
    var phoneNumber: String?

    // Stripe Connect Fields
    var stripeAccountId: String?
    var stripeAccountStatus: String = "not_started"
    var stripeAccountCreatedAt: Date?
    var stripePaymentMethodId: String?
    var paymentMethodSetupAt: Date?
    var canJoinSplits: Bool = false
    var identityVerified: Bool = false
    var identityVerificationDate: Date?

    // Enhanced Security Fields
    var twoFactorEnabled: Bool = false
    var twoFactorSetupDate: Date?
    var trustedDevices: [TrustedDevice] = []
    var lastSecurityAudit: Date?
    var securityLevel: SecurityLevel = .basic
    var failedLoginAttempts: Int = 0
    var lastFailedLoginAttempt: Date?
    var accountLockedUntil: Date?
    var biometricEnabled: Bool = false
    var sessionTimeout: TimeInterval = 900 // 15 minutes default
    var requireTwoFactorForTransactions: Bool = false
    var backupCodesUsed: [String] = []
    var lastPasswordChange: Date?
    var securityNotificationsEnabled: Bool = true


    
    // Custom initializer to handle missing fields gracefully
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        // Required fields
        id = try container.decodeIfPresent(String.self, forKey: .id)
        uid = try container.decodeIfPresent(String.self, forKey: .uid)
        email = try container.decodeIfPresent(String.self, forKey: .email)
        displayName = try container.decodeIfPresent(String.self, forKey: .displayName)
        balance = try container.decodeIfPresent(Double.self, forKey: .balance) ?? 0.0
        phoneNumber = try container.decodeIfPresent(String.self, forKey: .phoneNumber)
        
        // Security fields with defaults
        twoFactorEnabled = try container.decodeIfPresent(Bool.self, forKey: .twoFactorEnabled) ?? false
        twoFactorSetupDate = try container.decodeIfPresent(Date.self, forKey: .twoFactorSetupDate)
        trustedDevices = try container.decodeIfPresent([TrustedDevice].self, forKey: .trustedDevices) ?? []
        lastSecurityAudit = try container.decodeIfPresent(Date.self, forKey: .lastSecurityAudit)
        securityLevel = try container.decodeIfPresent(SecurityLevel.self, forKey: .securityLevel) ?? .basic
        failedLoginAttempts = try container.decodeIfPresent(Int.self, forKey: .failedLoginAttempts) ?? 0
        lastFailedLoginAttempt = try container.decodeIfPresent(Date.self, forKey: .lastFailedLoginAttempt)
        accountLockedUntil = try container.decodeIfPresent(Date.self, forKey: .accountLockedUntil)
        biometricEnabled = try container.decodeIfPresent(Bool.self, forKey: .biometricEnabled) ?? false
        sessionTimeout = try container.decodeIfPresent(TimeInterval.self, forKey: .sessionTimeout) ?? 900
        requireTwoFactorForTransactions = try container.decodeIfPresent(Bool.self, forKey: .requireTwoFactorForTransactions) ?? false
        backupCodesUsed = try container.decodeIfPresent([String].self, forKey: .backupCodesUsed) ?? []
        lastPasswordChange = try container.decodeIfPresent(Date.self, forKey: .lastPasswordChange)
        securityNotificationsEnabled = try container.decodeIfPresent(Bool.self, forKey: .securityNotificationsEnabled) ?? true


    }
    
    // Default initializer
    init(id: String? = nil, uid: String? = nil, email: String? = nil, displayName: String? = nil, balance: Double? = nil, phoneNumber: String? = nil) {
        self.id = id
        self.uid = uid
        self.email = email
        self.displayName = displayName
        self.balance = balance ?? 0.0
        self.phoneNumber = phoneNumber

        // Set defaults for Stripe Connect fields
        self.stripeAccountId = nil
        self.stripeAccountStatus = "not_started"
        self.stripeAccountCreatedAt = nil
        self.stripePaymentMethodId = nil
        self.paymentMethodSetupAt = nil
        self.canJoinSplits = false
        self.identityVerified = false
        self.identityVerificationDate = nil

        // Set defaults for security fields
        self.twoFactorEnabled = false
        self.twoFactorSetupDate = nil
        self.trustedDevices = []
        self.lastSecurityAudit = nil
        self.securityLevel = .basic
        self.failedLoginAttempts = 0
        self.lastFailedLoginAttempt = nil
        self.accountLockedUntil = nil
        self.biometricEnabled = false
        self.sessionTimeout = 900
        self.requireTwoFactorForTransactions = false
        self.backupCodesUsed = []
        self.lastPasswordChange = nil
        self.securityNotificationsEnabled = true


    }
    
    // CodingKeys enum
    enum CodingKeys: String, CodingKey {
        case id, uid, email, displayName, balance, phoneNumber
        case stripeAccountId, stripeAccountStatus, stripeAccountCreatedAt
        case stripePaymentMethodId, paymentMethodSetupAt, canJoinSplits
        case identityVerified, identityVerificationDate
        case twoFactorEnabled, twoFactorSetupDate, trustedDevices, lastSecurityAudit
        case securityLevel, failedLoginAttempts, lastFailedLoginAttempt, accountLockedUntil
        case biometricEnabled, sessionTimeout, requireTwoFactorForTransactions
        case backupCodesUsed, lastPasswordChange, securityNotificationsEnabled

    }
    
    // Computed Properties
    var initial: String {
        return String(displayName?.first ?? email?.first ?? "U").uppercased()
    }
    
    var isAccountLocked: Bool {
        guard let lockedUntil = accountLockedUntil else { return false }
        return Date() < lockedUntil
    }
    
    var shouldPromptFor2FA: Bool {
        return !twoFactorEnabled && (twoFactorSetupDate == nil ||
               Date().timeIntervalSince(twoFactorSetupDate ?? Date()) > 86400 * 7) // 7 days
    }


    
    var securityScore: Int {
        var score = 0
        if twoFactorEnabled { score += 30 }
        if biometricEnabled { score += 20 }
        if !trustedDevices.isEmpty { score += 15 }
        if phoneNumber != nil && !phoneNumber!.isEmpty { score += 10 }
        if securityLevel == .enhanced { score += 15 }
        if securityLevel == .maximum { score += 25 }
        if requireTwoFactorForTransactions { score += 10 }
        return min(score, 100)
    }

    // MARK: - Stripe Connect Computed Properties

    var stripeAccountStatusEnum: StripeAccountStatus {
        return StripeAccountStatus(rawValue: stripeAccountStatus) ?? .notStarted
    }

    var canAcceptPayments: Bool {
        return stripeAccountStatusEnum == .active && canJoinSplits
    }

    var needsStripeOnboarding: Bool {
        return stripeAccountId == nil || stripeAccountStatusEnum == .notStarted || stripeAccountStatusEnum == .pendingOnboarding
    }

    var needsPaymentMethodSetup: Bool {
        return stripePaymentMethodId == nil
    }

    var needsIdentityVerification: Bool {
        return !identityVerified
    }

    var isFullySetupForSplits: Bool {
        return canAcceptPayments && !needsPaymentMethodSetup && identityVerified
    }
}

// MARK: - Stripe Account Status Enum

enum StripeAccountStatus: String, Codable, CaseIterable {
    case notStarted = "not_started"
    case pendingOnboarding = "pending_onboarding"
    case pendingVerification = "pending_verification"
    case active = "active"
    case rejected = "rejected"
    case restricted = "restricted"

    var displayName: String {
        switch self {
        case .notStarted: return "Not Started"
        case .pendingOnboarding: return "Pending Onboarding"
        case .pendingVerification: return "Pending Verification"
        case .active: return "Active"
        case .rejected: return "Rejected"
        case .restricted: return "Restricted"
        }
    }

    var canAcceptPayments: Bool {
        return self == .active
    }
}
