// Modern JavaScript for Dash Website
document.addEventListener('DOMContentLoaded', function() {

  // Get elements
  const navbar = document.getElementById('navbar');
  const hamburger = document.getElementById('hamburger');
  const navMenu = document.getElementById('navMenu');
  const mobileMenu = document.getElementById('mobileMenu');
  const mobileMenuClose = document.getElementById('mobileMenuClose');
  const navLinks = document.querySelectorAll('.nav-link');
  const downloadBtns = document.querySelectorAll('#downloadBtn, #heroDownload, #ctaDownload, #mobileDownloadBtn');
  const downloadModal = document.getElementById('downloadModal');
  const modalClose = document.getElementById('modalClose');

  // Sliding indicator functionality
  let indicatorInitialized = false;

  function initSlidingIndicator() {
    if (!navMenu || indicatorInitialized || window.innerWidth <= 768) return;

    // Force a reflow to ensure elements are positioned
    navMenu.offsetHeight;

    setTimeout(() => {
      const activeLink = document.querySelector('.nav-link.active');
      if (activeLink) {
        // Ensure all elements are properly measured
        const allLinks = Array.from(navMenu.querySelectorAll('.nav-link'));
        allLinks.forEach(link => link.offsetWidth); // Force layout calculation

        updateIndicatorPosition(activeLink, true);
        indicatorInitialized = true;
      }
    }, 100);
  }

  function updateIndicatorPosition(activeLink, immediate = false) {
    if (!activeLink || !navMenu) return;

    // Skip if we're on mobile (indicator is hidden)
    if (window.innerWidth <= 768) return;

    // Get all nav links to calculate position
    const allLinks = Array.from(navMenu.querySelectorAll('.nav-link'));
    const linkIndex = allLinks.indexOf(activeLink);

    if (linkIndex === -1) return;

    // Calculate position based on link index and actual measurements
    const navMenuPadding = 6; // CSS padding
    const linkGap = 8; // CSS gap between links

    let offsetLeft = navMenuPadding;

    // Add up widths of previous links plus gaps
    for (let i = 0; i < linkIndex; i++) {
      offsetLeft += allLinks[i].offsetWidth + linkGap;
    }

    const linkWidth = activeLink.offsetWidth;

    console.log('Indicator update:', {
      activeLink: activeLink.textContent,
      linkIndex: linkIndex,
      offsetLeft: offsetLeft,
      linkWidth: linkWidth,
      allLinkWidths: allLinks.map(l => l.offsetWidth)
    });

    // Apply the position
    if (immediate) {
      navMenu.style.setProperty('--indicator-left', `${offsetLeft}px`);
      navMenu.style.setProperty('--indicator-width', `${linkWidth}px`);
    } else {
      // Use requestAnimationFrame for smooth updates
      requestAnimationFrame(() => {
        navMenu.style.setProperty('--indicator-left', `${offsetLeft}px`);
        navMenu.style.setProperty('--indicator-width', `${linkWidth}px`);
      });
    }
  }

  // Handle navigation link clicks
  navLinks.forEach((link, index) => {
    link.addEventListener('click', function(e) {
      // Remove active class from all links
      navLinks.forEach(l => l.classList.remove('active'));

      // Add active class to clicked link
      this.classList.add('active');

      // Update sliding indicator position
      updateIndicatorPosition(this);

      // Handle home link specifically
      if (this.getAttribute('href') === '#home') {
        e.preventDefault();
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      }
    });
  });

  // Initialize after DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initSlidingIndicator);
  } else {
    initSlidingIndicator();
  }

  // Handle window resize
  window.addEventListener('resize', () => {
    indicatorInitialized = false;
    setTimeout(initSlidingIndicator, 100);
  });

  // Liquid Glass Navbar scroll effect
  if (navbar) {
    let lastScrollY = window.scrollY;

    window.addEventListener('scroll', () => {
      const currentScrollY = window.scrollY;

      // Add scrolled class for enhanced glass effect
      if (currentScrollY > 50) {
        navbar.classList.add('scrolled');
      } else {
        navbar.classList.remove('scrolled');
      }

      // Hide/show navbar on scroll (only on mobile)
      if (window.innerWidth <= 768) {
        if (currentScrollY > lastScrollY && currentScrollY > 200) {
          navbar.style.transform = 'translateX(-50%) translateY(-100%)';
        } else {
          navbar.style.transform = 'translateX(-50%) translateY(0)';
        }
      } else {
        // Reset transform for desktop
        navbar.style.transform = 'translateX(-50%)';
      }

      lastScrollY = currentScrollY;
    });

    // Handle window resize
    window.addEventListener('resize', () => {
      if (window.innerWidth > 768) {
        navbar.style.transform = 'translateX(-50%)';
        // Close mobile menu if open
        if (navMenu && navMenu.classList.contains('show')) {
          hamburger.classList.remove('active');
          navMenu.classList.remove('show');
          document.body.style.overflow = '';
        }
      }
    });
  }

  // Mobile menu functions
  function closeMobileMenu() {
    hamburger.classList.remove('active');
    mobileMenu.classList.remove('show');
    document.body.style.overflow = '';
  }

  function openMobileMenu() {
    hamburger.classList.add('active');
    mobileMenu.classList.add('show');
    document.body.style.overflow = 'hidden';
  }

  // Mobile menu toggle
  if (hamburger && mobileMenu) {
    console.log('Mobile menu initialized', { hamburger, mobileMenu });

    hamburger.addEventListener('click', function(e) {
      e.stopPropagation();
      console.log('Hamburger clicked');

      if (mobileMenu.classList.contains('show')) {
        closeMobileMenu();
      } else {
        openMobileMenu();
      }
    });

    // Close button functionality
    if (mobileMenuClose) {
      mobileMenuClose.addEventListener('click', function(e) {
        e.stopPropagation();
        console.log('Close button clicked');
        closeMobileMenu();
      });
    }

    // Close menu when clicking on mobile nav links
    mobileMenu.querySelectorAll('.nav-link, .primary-button').forEach(link => {
      link.addEventListener('click', () => {
        closeMobileMenu();
      });
    });

    // Close menu when clicking outside
    document.addEventListener('click', (e) => {
      if (mobileMenu.classList.contains('show') &&
          !mobileMenu.contains(e.target) &&
          !hamburger.contains(e.target)) {
        closeMobileMenu();
      }
    });

    // Close menu with Escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && mobileMenu.classList.contains('show')) {
        closeMobileMenu();
      }
    });
  }

  // Download modal functionality
  downloadBtns.forEach(btn => {
    if (btn) {
      btn.addEventListener('click', function(e) {
        e.preventDefault();
        if (downloadModal) {
          downloadModal.classList.add('show');
          document.body.style.overflow = 'hidden';
        }
      });
    }
  });

  // Close modal
  function closeModal() {
    if (downloadModal) {
      downloadModal.classList.remove('show');
      document.body.style.overflow = '';
    }
  }

  if (modalClose) {
    modalClose.addEventListener('click', closeModal);
  }

  // Close modal when clicking outside
  if (downloadModal) {
    downloadModal.addEventListener('click', function(e) {
      if (e.target === downloadModal) {
        closeModal();
      }
    });
  }

  // Close modal with Escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && downloadModal && downloadModal.classList.contains('show')) {
      closeModal();
    }
  });

  // Smooth scrolling for anchor links
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
      e.preventDefault();
      const target = document.querySelector(this.getAttribute('href'));
      if (target) {
        const offsetTop = target.offsetTop - 80; // Account for fixed navbar
        window.scrollTo({
          top: offsetTop,
          behavior: 'smooth'
        });
      }
    });
  });

  // Intersection Observer for animations
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  };

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.style.opacity = '1';
        entry.target.style.transform = 'translateY(0)';
      }
    });
  }, observerOptions);

  // Observe elements for animation
  document.querySelectorAll('.feature-card, .testimonial-card, .team-card, .step').forEach(el => {
    el.style.opacity = '0';
    el.style.transform = 'translateY(30px)';
    el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
    observer.observe(el);
  });

  // NUCLEAR OPTION: Remove ALL navigation elements on mobile
  function nukeAllNavOnMobile() {
    if (window.innerWidth <= 768) {
      console.log('MOBILE DETECTED - REMOVING ALL NAV ELEMENTS');

      // Remove desktop menu
      const desktopMenu = document.getElementById('navMenu');
      if (desktopMenu) {
        desktopMenu.remove();
        console.log('✅ Desktop menu REMOVED');
      }

      // Remove any other nav elements that might be showing
      const allNavMenus = document.querySelectorAll('.nav-menu');
      allNavMenus.forEach((menu, index) => {
        menu.remove();
        console.log(`✅ Nav menu ${index + 1} REMOVED`);
      });

      // Remove sliding indicators
      const indicators = document.querySelectorAll('.sliding-indicator');
      indicators.forEach((indicator, index) => {
        indicator.remove();
        console.log(`✅ Sliding indicator ${index + 1} REMOVED`);
      });

      // Log what's left in navbar
      const navbar = document.querySelector('.navbar');
      if (navbar) {
        console.log('Navbar contents after cleanup:', navbar.innerHTML);
      }
    }
  }

  // Call immediately and on resize
  nukeAllNavOnMobile();
  window.addEventListener('resize', nukeAllNavOnMobile);

  console.log('🚀 Dash website loaded successfully!');
});
