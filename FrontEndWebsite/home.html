<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="Dash - Split payments instantly with friends. The modern way to share expenses." />
  <meta name="keywords" content="split payments, expense sharing, fintech, mobile payments" />
  <title>Dash - Split payments instantly</title>
  <link rel="stylesheet" href="CSS/style.css" />
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <script defer src="JS/script.js"></script>
</head>
<body>
  <!-- Navigation -->
  <nav class="navbar" id="navbar">
    <div class="nav-container">
      <div class="nav-brand">
        <img src="images/appicon.png" alt="Dash" class="nav-logo">
        <span class="nav-title">Dash <span style="margin: 0 12px;">|</span> The New Way to Pay</span>
      </div>

      <div class="nav-menu" id="navMenu">
        <div class="sliding-indicator"></div>
        <a href="#home" class="nav-link active" data-page="home">Home</a>
        <a href="aboutus.html" class="nav-link" data-page="about">About</a>
        <button class="primary-button small" id="downloadBtn">
          <span>Download for iOS</span>
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
            <polyline points="7,10 12,15 17,10"/>
            <line x1="12" y1="15" x2="12" y2="3"/>
          </svg>
        </button>
      </div>

      <div class="hamburger" id="hamburger">
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>
  </nav>

  <!-- Mobile Menu -->
  <div class="mobile-menu" id="mobileMenu">
    <button class="mobile-menu-close" id="mobileMenuClose">
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <line x1="18" y1="6" x2="6" y2="18"></line>
        <line x1="6" y1="6" x2="18" y2="18"></line>
      </svg>
    </button>
    <a href="#home" class="nav-link">Home</a>
    <a href="aboutus.html" class="nav-link">About</a>
    <button class="primary-button small" id="mobileDownloadBtn">
      <span>Download for iOS</span>
      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
        <polyline points="7,10 12,15 17,10"/>
        <line x1="12" y1="15" x2="12" y2="3"/>
      </svg>
    </button>
  </div>

  <!-- Hero Section -->
  <section class="hero" id="home">
    <div class="hero-container">
      <div class="hero-content">
        <div class="hero-badge">
          <span><strong>Split Payments, <u>Instantly.</u></span>
        </div>
        <h1 class="hero-title">
          The modern way to
          <span class="gradient-text">split expenses</span>
        </h1>
        <p class="hero-description">
          Stop that awkward money conversation. Dash makes splitting bills with friends as easy as sending a text.
        </p>
        <div class="hero-actions">
          <button class="primary-button small" id="heroDownload">
            <span>Download for iOS</span>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
              <polyline points="7,10 12,15 17,10"/>
              <line x1="12" y1="15" x2="12" y2="3"/>
            </svg>
          </button>
        </div>
        <div class="hero-stats">
          <div class="stat">
            <span class="stat-number"></span>
            <span class="stat-label"></span>
          </div>
          <div class="stat">
            <span class="stat-number"></span>
            <span class="stat-label"></span>
          </div>
          <div class="stat">
            <span class="stat-number"></span>
            <span class="stat-label"></span>
          </div>
        </div>
      </div>
      <div class="hero-visual">
        <div class="phone-mockup">
          <img src="images/DashBanner.png" alt="Dash App Interface" class="app-screenshot">
        </div>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section class="features" id="features">
    <div class="container">
      <div class="section-header">
        <h2>Everything you need to split smarter</h2>
        <p>Powerful features designed to make group payments effortless</p>
      </div>

      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 2L2 7l10 5 10-5-10-5z"/>
              <path d="M2 17l10 5 10-5"/>
              <path d="M2 12l10 5 10-5"/>
            </svg>
          </div>
          <h3>Smart Splitting</h3>
          <p>Automatically calculate who owes what with our intelligent splitting algorithms</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
              <line x1="16" y1="2" x2="16" y2="6"/>
              <line x1="8" y1="2" x2="8" y2="6"/>
              <line x1="3" y1="10" x2="21" y2="10"/>
            </svg>
          </div>
          <h3>Real-time Updates</h3>
          <p>See balance changes instantly as expenses are added and payments are made</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              <path d="M9 9h6v6H9z"/>
            </svg>
          </div>
          <h3>QR Payments</h3>
          <p>Pay friends instantly by scanning QR codes - no bank details needed</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
            </svg>
          </div>
          <h3>Bank-level Security</h3>
          <p>Your financial data is protected with enterprise-grade encryption</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"/>
              <polyline points="12,6 12,12 16,14"/>
            </svg>
          </div>
          <h3>Instant Settlements</h3>
          <p>Settle debts immediately with integrated payment processing</p>
        </div>

        <div class="feature-card">
          <div class="feature-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/>
              <circle cx="9" cy="7" r="4"/>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"/>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
            </svg>
          </div>
          <h3>Group Management</h3>
          <p>Create and manage multiple groups for different occasions and friend circles</p>
        </div>
      </div>
    </div>
  </section>

  <!-- How It Works Section -->
  <section class="how-it-works">
    <div class="container">
      <div class="section-header">
        <h2>How Dash works</h2>
        <p>Get started in three simple steps</p>
      </div>

      <div class="steps-container">
        <div class="step">
          <div class="step-number">1</div>
          <div class="step-content">
            <h3>Create a group</h3>
            <p>Add friends and start tracking shared expenses together</p>
          </div>
        </div>

        <div class="step">
          <div class="step-number">2</div>
          <div class="step-content">
            <h3>Add expenses</h3>
            <p>Snap photos of receipts or manually enter amounts</p>
          </div>
        </div>

        <div class="step">
          <div class="step-number">3</div>
          <div class="step-content">
            <h3>Settle instantly</h3>
            <p>Pay friends directly through the app with one tap</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Testimonials Section -->
  <section class="testimonials">
    <div class="container">
      <div class="section-header">
        <h2>Loved by thousands</h2>
        <p>See what our users are saying</p>
      </div>

      <div class="testimonials-grid">
        <div class="testimonial-card">
          <div class="testimonial-content">
            <p>"Finally, no more awkward IOUs. Dash makes everything so simple!"</p>
          </div>
          <div class="testimonial-author">
            <div class="author-avatar">J</div>
            <div class="author-info">
              <span class="author-name">Jane D.</span>
              <span class="author-title">College Student</span>
            </div>
          </div>
        </div>

        <div class="testimonial-card">
          <div class="testimonial-content">
            <p>"The app saved our trip. Super easy to use and everyone could track expenses."</p>
          </div>
          <div class="testimonial-author">
            <div class="author-avatar">A</div>
            <div class="author-info">
              <span class="author-name">Alex M.</span>
              <span class="author-title">Travel Enthusiast</span>
            </div>
          </div>
        </div>

        <div class="testimonial-card">
          <div class="testimonial-content">
            <p>"Perfect for roommates. No more spreadsheets or forgotten payments!"</p>
          </div>
          <div class="testimonial-author">
            <div class="author-avatar">S</div>
            <div class="author-info">
              <span class="author-name">Sarah K.</span>
              <span class="author-title">Working Professional</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- CTA Section -->
  <section class="cta-section">
    <div class="container">
      <div class="cta-content">
        <h2>Ready to split smarter?</h2>
        <p>Join thousands of users who've simplified their group payments</p>
        <button class="primary-button large" id="ctaDownload">
          <span>Download Dash Now</span>
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
            <polyline points="7,10 12,15 17,10"/>
            <line x1="12" y1="15" x2="12" y2="3"/>
          </svg>
        </button>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-brand">
          <img src="images/appicon.png" alt="Dash" class="footer-logo">
          <span class="footer-title">Dash</span>
          <p>The modern way to split expenses</p>
        </div>

        <div class="footer-links">
          <div class="footer-column">
            <h4>Product</h4>
            <a href="#features">Features</a>
            <a href="#pricing">Pricing</a>
            <a href="#security">Security</a>
          </div>

          <div class="footer-column">
            <h4>Company</h4>
            <a href="aboutus.html">About Us</a>
            <a href="#careers">Careers</a>
            <a href="#contact">Contact</a>
          </div>

          <div class="footer-column">
            <h4>Support</h4>
            <a href="#help">Help Center</a>
            <a href="#privacy">Privacy Policy</a>
            <a href="#terms">Terms of Service</a>
          </div>
        </div>
      </div>

      <div class="footer-bottom">
        <p>&copy; 2025 Dash. All rights reserved.</p>
        <div class="footer-social">
          <a href="#" aria-label="Twitter">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"/>
            </svg>
          </a>
          <a href="#" aria-label="LinkedIn">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"/>
              <rect x="2" y="9" width="4" height="12"/>
              <circle cx="4" cy="4" r="2"/>
            </svg>
          </a>
        </div>
      </div>
    </div>
  </footer>

  <!-- Download Modal -->
  <div id="downloadModal" class="modal-overlay">
    <div class="modal-content">
      <button class="modal-close" id="modalClose">&times;</button>
      <div class="modal-header">
        <h3>Coming Soon</h3>
        <p>Enter your email and we'll notify you when Dash launches.</p>
      </div>
      <form
        action="https://formspree.io/f/mgvzzwgz"
        method="POST"
        id="notifyForm"
        class="download-options"
      >
        <input
          type="email"
          name="email"
          id="emailInput"
          placeholder="Enter your email"
          required
          style="padding: 10px; width: 100%; max-width: 300px; border-radius: 8px; border: 1px solid #ccc;"
        />
        <button type="submit" class="primary-button small">Notify Me</button>
        <p id="notifyMessage" style="margin-top: 10px; color: green; display: none;">Thanks! We'll keep you posted.</p>
      </form>
    </div>
  </div>

</body>
</html>